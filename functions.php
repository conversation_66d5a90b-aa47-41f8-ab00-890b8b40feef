<?php
// Include custom roles functionality
require get_template_directory() . '/inc/custom-roles.php';

// Include Bootstrap 5 Nav Menu Walker
require_once get_template_directory() . '/inc/class-bootstrap-5-nav-menu-walker.php';

// Include translations system files
require get_template_directory() . '/inc/translations-system.php';
require get_template_directory() . '/inc/translations-system-display.php';
require get_template_directory() . '/inc/translations-selection.php';

// Include Elite Badge functionality
require get_template_directory() . '/inc/elite-badge.php';
require get_template_directory() . '/inc/elite-badge-widget.php';

// Include Novel Ranking Widget
require get_template_directory() . '/inc/novel-ranking-widget.php';

// Include Novel Views Tracker
require get_template_directory() . '/inc/novel-views-tracker.php';

/**
 * Sekaiplus theme functions and definitions
 */

if (!defined('SEKAIPLUS_VERSION')) {
    define('SEKAIPLUS_VERSION', '1.0.0');
}

/**
 * Theme Setup
 */
function sekaiplus_setup() {
    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable post thumbnails
    add_theme_support('post-thumbnails');

    // Enable custom logo
    add_theme_support('custom-logo', array(
        'height'      => 60,
        'width'       => 200,
        'flex-width'  => true,
        'flex-height' => true,
    ));

    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('القائمة الرئيسية', 'sekaiplus'),
        'footer'  => __('قائمة الفوتر', 'sekaiplus'),
    ));

    // Switch default core markup to output valid HTML5
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));

    // Add theme support for selective refresh for widgets
    add_theme_support('customize-selective-refresh-widgets');

    // Add support for RTL
    add_theme_support('rtl');

    // Add editor styles
    add_theme_support('editor-styles');
    add_editor_style('css/editor-style.css');

    // Custom image sizes
    add_image_size('novel-cover', 400, 600, true);
}
add_action('after_setup_theme', 'sekaiplus_setup');

/**
 * Register Custom Post Types
 */
function sekaiplus_register_post_types() {
    // Novel Post Type
    register_post_type('novel', array(
        'labels' => array(
            'name'               => __('الروايات', 'sekaiplus'),
            'singular_name'      => __('رواية', 'sekaiplus'),
            'add_new'           => __('إضافة رواية', 'sekaiplus'),
            'add_new_item'      => __('إضافة رواية جديدة', 'sekaiplus'),
            'edit_item'         => __('تعديل الرواية', 'sekaiplus'),
            'new_item'          => __('رواية جديدة', 'sekaiplus'),
            'view_item'         => __('عرض الرواية', 'sekaiplus'),
            'search_items'      => __('بحث في الروايات', 'sekaiplus'),
            'not_found'         => __('لم يتم العثور على روايات', 'sekaiplus'),
            'menu_name'         => __('الروايات', 'sekaiplus'),
        ),
        'public'       => true,
        'has_archive'  => true,
        'supports'     => array('title', 'editor', 'thumbnail', 'comments', 'author'),
        'menu_icon'    => 'dashicons-book',
        'rewrite'      => array('slug' => 'novels'),
    ));

    // Chapter Post Type
    register_post_type('chapter', array(
        'labels' => array(
            'name'               => __('الفصول', 'sekaiplus'),
            'singular_name'      => __('فصل', 'sekaiplus'),
            'add_new'           => __('إضافة فصل', 'sekaiplus'),
            'add_new_item'      => __('إضافة فصل جديد', 'sekaiplus'),
            'edit_item'         => __('تعديل الفصل', 'sekaiplus'),
            'new_item'          => __('فصل جديد', 'sekaiplus'),
            'view_item'         => __('عرض الفصل', 'sekaiplus'),
            'search_items'      => __('بحث في الفصول', 'sekaiplus'),
            'not_found'         => __('لم يتم العثور على فصول', 'sekaiplus'),
            'menu_name'         => __('الفصول', 'sekaiplus'),
        ),
        'public'       => true,
        'has_archive'  => true,
        'supports'     => array('title', 'editor', 'comments', 'author'),
        'menu_icon'    => 'dashicons-format-aside',
        'rewrite'      => array('slug' => 'chapters'),
    ));

    // Volume Post Type
    register_post_type('volume', array(
        'labels' => array(
            'name'               => __('المجلدات', 'sekaiplus'),
            'singular_name'      => __('مجلد', 'sekaiplus'),
            'add_new'           => __('إضافة مجلد', 'sekaiplus'),
            'add_new_item'      => __('إضافة مجلد جديد', 'sekaiplus'),
            'edit_item'         => __('تعديل المجلد', 'sekaiplus'),
            'new_item'          => __('مجلد جديد', 'sekaiplus'),
            'view_item'         => __('عرض المجلد', 'sekaiplus'),
            'search_items'      => __('بحث في المجلدات', 'sekaiplus'),
            'not_found'         => __('لم يتم العثور على مجلدات', 'sekaiplus'),
            'menu_name'         => __('المجلدات', 'sekaiplus'),
        ),
        'public'       => true,
        'has_archive'  => true,
        'supports'     => array('title', 'editor', 'thumbnail', 'author'),
        'menu_icon'    => 'dashicons-book-alt',
        'rewrite'      => array('slug' => 'volumes'),
    ));
}
add_action('init', 'sekaiplus_register_post_types');

/**
 * Register Taxonomies
 */
function sekaiplus_register_taxonomies() {
    // Genre Taxonomy
    register_taxonomy('genre', 'novel', array(
        'labels' => array(
            'name'              => __('التصنيفات', 'sekaiplus'),
            'singular_name'     => __('تصنيف', 'sekaiplus'),
            'search_items'      => __('بحث في التصنيفات', 'sekaiplus'),
            'all_items'         => __('جميع التصنيفات', 'sekaiplus'),
            'edit_item'         => __('تعديل التصنيف', 'sekaiplus'),
            'update_item'       => __('تحديث التصنيف', 'sekaiplus'),
            'add_new_item'      => __('إضافة تصنيف جديد', 'sekaiplus'),
            'menu_name'         => __('التصنيفات', 'sekaiplus'),
        ),
        'hierarchical'      => true,
        'show_ui'          => true,
        'show_admin_column' => true,
        'query_var'        => true,
        'rewrite'          => array('slug' => 'genre'),
    ));

    // Status Taxonomy
    register_taxonomy('status', 'novel', array(
        'labels' => array(
            'name'              => __('الحالة', 'sekaiplus'),
            'singular_name'     => __('حالة', 'sekaiplus'),
            'search_items'      => __('بحث في الحالات', 'sekaiplus'),
            'all_items'         => __('جميع الحالات', 'sekaiplus'),
            'edit_item'         => __('تعديل الحالة', 'sekaiplus'),
            'update_item'       => __('تحديث الحالة', 'sekaiplus'),
            'add_new_item'      => __('إضافة حالة جديدة', 'sekaiplus'),
            'menu_name'         => __('الحالات', 'sekaiplus'),
        ),
        'hierarchical'      => true,
        'show_ui'          => true,
        'show_admin_column' => true,
        'query_var'        => true,
        'rewrite'          => array('slug' => 'status'),
    ));
}
add_action('init', 'sekaiplus_register_taxonomies');

/**
 * Register Author and Illustrator Taxonomies
 */
function sekaiplus_register_creator_taxonomies() {
    // Author Taxonomy
    register_taxonomy('novel_author', 'novel', array(
        'labels' => array(
            'name'              => __('المؤلفون', 'sekaiplus'),
            'singular_name'     => __('مؤلف', 'sekaiplus'),
            'search_items'      => __('بحث في المؤلفين', 'sekaiplus'),
            'all_items'         => __('جميع المؤلفين', 'sekaiplus'),
            'edit_item'         => __('تعديل المؤلف', 'sekaiplus'),
            'update_item'       => __('تحديث المؤلف', 'sekaiplus'),
            'add_new_item'      => __('إضافة مؤلف جديد', 'sekaiplus'),
            'new_item_name'     => __('اسم المؤلف الجديد', 'sekaiplus'),
            'menu_name'         => __('المؤلفون', 'sekaiplus'),
        ),
        'hierarchical'      => false,
        'show_ui'          => true,
        'show_admin_column' => true,
        'query_var'        => true,
        'rewrite'          => array('slug' => 'author'),
    ));

    // Illustrator Taxonomy
    register_taxonomy('novel_illustrator', 'novel', array(
        'labels' => array(
            'name'              => __('الرسامون', 'sekaiplus'),
            'singular_name'     => __('رسام', 'sekaiplus'),
            'search_items'      => __('بحث في الرسامين', 'sekaiplus'),
            'all_items'         => __('جميع الرسامين', 'sekaiplus'),
            'edit_item'         => __('تعديل الرسام', 'sekaiplus'),
            'update_item'       => __('تحديث الرسام', 'sekaiplus'),
            'add_new_item'      => __('إضافة رسام جديد', 'sekaiplus'),
            'new_item_name'     => __('اسم الرسام الجديد', 'sekaiplus'),
            'menu_name'         => __('الرسامون', 'sekaiplus'),
        ),
        'hierarchical'      => false,
        'show_ui'          => true,
        'show_admin_column' => true,
        'query_var'        => true,
        'rewrite'          => array('slug' => 'illustrator'),
    ));
}
add_action('init', 'sekaiplus_register_creator_taxonomies');

/**
 * Enqueue scripts and styles
 */
function sekaiplus_enqueue_scripts() {
    // Bootstrap
    wp_enqueue_style('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css');
    wp_enqueue_script('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js', array('jquery'), null, true);

    // Font Awesome
    wp_enqueue_style('fontawesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

    // Swiper Slider
    wp_enqueue_style('swiper', 'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css');
    wp_enqueue_script('swiper', 'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js', array(), null, true);

    // Theme styles
    wp_enqueue_style('sekaiplus-style', get_stylesheet_uri(), array(), SEKAIPLUS_VERSION);
    
    // Theme scripts
    wp_enqueue_script('sekaiplus-scripts', get_template_directory_uri() . '/js/scripts.js', array('jquery'), SEKAIPLUS_VERSION, true);

    // Pass data to JavaScript
    wp_localize_script('sekaiplus-scripts', 'sekaiplus', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('sekaiplus_ajax_nonce'),
        'is_rtl' => is_rtl(),
        'isLoggedIn' => is_user_logged_in(),
        'login_url' => wp_login_url(get_permalink()),
        'translations' => array(
            'addToBookmarks' => __('إضافة للقائمة', 'sekaiplus'),
            'removeFromBookmarks' => __('إزالة من القائمة', 'sekaiplus'),
            'loginRequired' => __('يجب تسجيل الدخول أولاً', 'sekaiplus')
        )
    ));
}
add_action('wp_enqueue_scripts', 'sekaiplus_enqueue_scripts');

/**
 * Enqueue scripts and styles for dark mode
 */
function sekaiplus_enqueue_dark_mode() {
    wp_enqueue_script('sekaiplus-dark-mode', get_template_directory_uri() . '/js/dark-mode.js', array('jquery'), '1.0', true);
    
    wp_localize_script('sekaiplus-dark-mode', 'darkModeVars', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('sekaiplus_dark_mode_nonce'),
        'isLoggedIn' => is_user_logged_in() ? '1' : '0'
    ));
}
add_action('wp_enqueue_scripts', 'sekaiplus_enqueue_dark_mode');

/**
 * Enqueue profile assets
 */
function enqueue_profile_assets() {
    if (is_page_template('template-profile.php')) {
        wp_enqueue_style('profile-styles', get_template_directory_uri() . '/assets/css/profile.css');
        wp_enqueue_script('profile-script', get_template_directory_uri() . '/assets/js/profile.js', array('jquery'), '', true);
    }
}
add_action('wp_enqueue_scripts', 'enqueue_profile_assets');

/**
 * Admin styles
 */
function sekaiplus_admin_styles() {
    global $post_type;
    if ($post_type == 'novel') {
        ?>
        <style>
        .novel-meta-section {
            margin-bottom: 20px;
            padding: 10px;
            background: #fff;
            border: 1px solid #ddd;
        }
        .novel-meta-section h4 {
            margin-top: 0;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        .volume-cover-item {
            margin-bottom: 15px;
            padding: 10px;
            background: #f9f9f9;
            border: 1px solid #eee;
        }
        .volume-cover-preview {
            max-width: 150px;
            height: auto;
            margin-top: 10px;
            display: block;
        }
        .creator-field {
            margin-bottom: 15px;
        }
        .add-new-creator {
            display: inline-block;
            margin-top: 5px;
            text-decoration: none;
        }
        .menu-item-settings .description {
            display: block;
            margin: 5px 0;
            color: #666;
        }
        </style>
        <?php
    }
}
add_action('admin_head', 'sekaiplus_admin_styles');

/**
 * Get novel rating
 */
function sekaiplus_get_novel_rating($novel_id) {
    $rating = get_post_meta($novel_id, '_novel_rating', true);
    return $rating ? round($rating, 1) : 0;
}

/**
 * Get chapter count
 */
function sekaiplus_get_chapter_count($novel_id) {
    global $wpdb;
    
    // تحقق من وجود النظام الجديد لفصل الترجمات
    $translation_system = get_option('sekaiplus_translation_system', 'new');
    
    if ($translation_system === 'new') {
        // احصل على كل chapter_unique_id الفريدة للرواية بدلاً من عد جميع المنشورات
        $unique_chapters = $wpdb->get_col($wpdb->prepare(
            "SELECT DISTINCT meta_value FROM {$wpdb->postmeta} pm 
            JOIN {$wpdb->posts} p ON p.ID = pm.post_id 
            WHERE pm.meta_key = 'chapter_unique_id' 
            AND p.post_type = 'chapter' 
            AND p.post_parent = %d",
            $novel_id
        ));
        
        return count($unique_chapters);
    } else {
        // النظام القديم: عد كل المنشورات
        $chapters = get_posts(array(
            'post_type' => 'chapter',
            'post_parent' => $novel_id,
            'posts_per_page' => -1,
            'fields' => 'ids'
        ));
        return count($chapters);
    }
}

/**
 * Get translator badge
 */
function sekaiplus_get_translator_badge() {
    if (current_user_can('translate_novels')) {
        return ' <span class="badge bg-primary">مترجم</span>';
    }
    return '';
}

/**
 * Ajax search handler
 */
function sekaiplus_ajax_search() {
    check_ajax_referer('sekaiplus_ajax_nonce', 'nonce');

    $query = isset($_POST['query']) ? sanitize_text_field($_POST['query']) : '';
    
    if (strlen($query) < 2) {
        wp_die();
    }

    // البحث في الروايات
    $novels = new WP_Query(array(
        'post_type' => 'novel',
        'posts_per_page' => 5,
        's' => $query,
        'meta_query' => array(
            'relation' => 'OR',
            // البحث في العناوين البديلة
            array(
                'key' => 'romaji_title',
                'value' => $query,
                'compare' => 'LIKE'
            ),
            array(
                'key' => 'japanese_title',
                'value' => $query,
                'compare' => 'LIKE'
            ),
            array(
                'key' => 'english_title',
                'value' => $query,
                'compare' => 'LIKE'
            )
        )
    ));

    // البحث في المترجمين
    $translators_query = new WP_User_Query(array(
        'search' => '*' . $query . '*',
        'role__in' => array('translator', 'administrator', 'editor'),
        'number' => 3,
        'search_columns' => array('display_name', 'user_login')
    ));
    $translators = $translators_query->get_results();

    // عرض نتائج الروايات
    if ($novels->have_posts() || !empty($translators)) {
        // عرض المترجمين أولاً
        if (!empty($translators)) {
            echo '<div class="search-category p-2 bg-light text-muted small">المترجمون</div>';
            foreach ($translators as $translator) {
                echo '<a href="' . get_author_posts_url($translator->ID) . '" class="search-result d-flex align-items-center gap-3 text-decoration-none p-3 border-bottom">';
                echo '<div class="translator-avatar">';
                echo get_avatar($translator->ID, 50, '', '', array('class' => 'rounded-circle'));
                echo '</div>';
                echo '<div class="flex-grow-1">';
                echo '<h6 class="mb-1 text-dark">' . $translator->display_name . '</h6>';
                echo '<small class="text-muted"><i class="fas fa-book me-1"></i> ' . count_user_posts($translator->ID, 'chapter') . ' فصل</small>';
                echo '</div>';
                echo '</a>';
            }
        }

        // عرض الروايات
        if ($novels->have_posts()) {
            echo '<div class="search-category p-2 bg-light text-muted small">الروايات</div>';
            while ($novels->have_posts()) {
                $novels->the_post();
                get_template_part('template-parts/content', 'search-result');
            }
        }
    } else {
        echo '<div class="p-4 text-center text-muted">لم يتم العثور على نتائج</div>';
    }

    wp_reset_postdata();
    wp_die();
}
add_action('wp_ajax_sekaiplus_quick_search', 'sekaiplus_ajax_search');
add_action('wp_ajax_nopriv_sekaiplus_quick_search', 'sekaiplus_ajax_search');

/**
 * Handle novel rating with 24-hour cooldown
 */
function sekaiplus_ajax_rate_novel() {
    // التحقق من الأمان
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'sekaiplus_ajax_nonce')) {
        wp_send_json_error(array(
            'message' => 'خطأ في التحقق من الأمان',
            'type' => 'error'
        ));
    }

    if (!is_user_logged_in()) {
        wp_send_json_error(array(
            'message' => 'يجب تسجيل الدخول أولاً',
            'type' => 'warning'
        ));
    }

    $novel_id = intval($_POST['novel_id']);
    $rating = intval($_POST['rating']);
    $user_id = get_current_user_id();

    // التحقق من صحة البيانات
    if (!$novel_id || $rating < 1 || $rating > 5) {
        wp_send_json_error(array(
            'message' => 'بيانات التقييم غير صالحة',
            'type' => 'error'
        ));
    }

    // التحقق من وجود الرواية
    if (get_post_type($novel_id) !== 'novel') {
        wp_send_json_error(array(
            'message' => 'الرواية غير موجودة',
            'type' => 'error'
        ));
    }

    // Get existing ratings
    $user_ratings = get_post_meta($novel_id, '_user_ratings', true);
    if (!is_array($user_ratings)) {
        $user_ratings = array();
    }

    $is_new_rating = !isset($user_ratings[$user_id]);

    // Check if user has already rated (24-hour cooldown)
    if (isset($user_ratings[$user_id])) {
        $last_rating_time = $user_ratings[$user_id]['time'];
        $hours_since_last_rating = (time() - $last_rating_time) / 3600;

        if ($hours_since_last_rating < 24) {
            $hours_remaining = ceil(24 - $hours_since_last_rating);
            wp_send_json_error(array(
                'message' => sprintf('لا يمكن تغيير تقييمك إلا بعد %d ساعة', $hours_remaining),
                'type' => 'info',
                'currentRating' => $user_ratings[$user_id]['rating']
            ));
        }
    }

    // Add or update rating
    $user_ratings[$user_id] = array(
        'rating' => $rating,
        'time' => time()
    );

    // Save ratings
    update_post_meta($novel_id, '_user_ratings', $user_ratings);

    // Calculate new average
    $total_rating = 0;
    foreach ($user_ratings as $rating_data) {
        $total_rating += $rating_data['rating'];
    }
    $average_rating = round($total_rating / count($user_ratings), 1);

    // Update average rating meta
    update_post_meta($novel_id, '_novel_rating', $average_rating);

    $message = $is_new_rating ? 'تم تسجيل تقييمك بنجاح' : 'تم تحديث تقييمك بنجاح';

    wp_send_json_success(array(
        'message' => $message,
        'type' => 'success',
        'averageRating' => $average_rating,
        'ratingCount' => count($user_ratings),
        'userRating' => $rating
    ));
}
add_action('wp_ajax_sekaiplus_rate_novel', 'sekaiplus_ajax_rate_novel');

/**
 * Handle bookmark toggle
 */
function sekaiplus_ajax_toggle_bookmark() {
    check_ajax_referer('sekaiplus_ajax_nonce', 'nonce');

    if (!is_user_logged_in()) {
        wp_send_json_error(__('يجب تسجيل الدخول أولاً', 'sekaiplus'));
    }

    $novel_id = isset($_POST['novel_id']) ? intval($_POST['novel_id']) : 0;
    if (!$novel_id) {
        wp_send_json_error(__('بيانات غير صالحة', 'sekaiplus'));
    }

    $user_id = get_current_user_id();
    $bookmarks = get_user_meta($user_id, '_novel_bookmarks', true) ?: array();
    $bookmark_count = intval(get_post_meta($novel_id, '_bookmark_count', true));

    if (in_array($novel_id, $bookmarks)) {
        // Remove bookmark
        $bookmarks = array_diff($bookmarks, array($novel_id));
        $bookmark_count--;
    } else {
        // Add bookmark
        $bookmarks[] = $novel_id;
        $bookmark_count++;
    }

    update_user_meta($user_id, '_novel_bookmarks', array_values($bookmarks));
    update_post_meta($novel_id, '_bookmark_count', max(0, $bookmark_count));

    wp_send_json_success(array(
        'bookmarked' => in_array($novel_id, $bookmarks),
        'count' => $bookmark_count
    ));
}
add_action('wp_ajax_sekaiplus_toggle_bookmark', 'sekaiplus_ajax_toggle_bookmark');

/**
 * Handle dark mode toggle
 */
function sekaiplus_toggle_dark_mode() {
    if (!is_user_logged_in() || !check_ajax_referer('sekaiplus_dark_mode_nonce', 'nonce', false)) {
        wp_send_json_error();
        return;
    }

    $user_id = get_current_user_id();
    $is_dark = isset($_POST['isDark']) ? $_POST['isDark'] === '1' : false;
    
    update_user_meta($user_id, 'sekaiplus_dark_mode', $is_dark);
    
    wp_send_json_success(array('isDark' => $is_dark));
}
add_action('wp_ajax_sekaiplus_toggle_dark_mode', 'sekaiplus_toggle_dark_mode');

/**
 * Custom Menu Walker
 */
class Sekaiplus_Menu_Walker extends Walker_Nav_Menu {
    function start_el(&$output, $item, $depth = 0, $args = array(), $id = 0) {
        $indent = ($depth) ? str_repeat("\t", $depth) : '';
        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;
        
        $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args, $depth));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';
        
        $id = apply_filters('nav_menu_item_id', 'menu-item-'. $item->ID, $item, $args, $depth);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';
        
        $output .= $indent . '<li' . $id . $class_names .'>';
        
        $atts = array();
        $atts['title']  = !empty($item->attr_title) ? $item->attr_title : '';
        $atts['target'] = !empty($item->target) ? $item->target : '';
        $atts['rel']    = !empty($item->xfn) ? $item->xfn : '';
        $atts['href']   = !empty($item->url) ? $item->url : '';
        
        $atts = apply_filters('nav_menu_link_attributes', $atts, $item, $args, $depth);
        
        $attributes = '';
        foreach ($atts as $attr => $value) {
            if (!empty($value)) {
                $value = ('href' === $attr) ? esc_url($value) : esc_attr($value);
                $attributes .= ' ' . $attr . '="' . $value . '"';
            }
        }
        
        $icon = get_post_meta($item->ID, '_menu_item_icon', true);
        $item_output = $args->before;
        $item_output .= '<a'. $attributes .'>';
        if ($icon) {
            $item_output .= '<i class="' . esc_attr($icon) . ' ms-1"></i>';
        }
        $item_output .= $args->link_before . apply_filters('the_title', $item->title, $item->ID) . $args->link_after;
        $item_output .= '</a>';
        $item_output .= $args->after;
        
        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }
}

/**
 * Add icon field to menu items
 */
function sekaiplus_add_menu_icon_field($id, $item) {
    $icon = get_post_meta($item->ID, '_menu_item_icon', true);
    ?>
    <div class="field-menu-icon description-wide" style="margin: 5px 0;">
        <label for="edit-menu-item-icon-<?php echo $item->ID; ?>">
            <?php _e('أيقونة القائمة (Font Awesome)', 'sekaiplus'); ?><br>
            <input type="text" 
                id="edit-menu-item-icon-<?php echo $item->ID; ?>" 
                class="widefat edit-menu-item-icon" 
                name="menu-item-icon[<?php echo $item->ID; ?>]" 
                value="<?php echo esc_attr($icon); ?>"
                placeholder="fas fa-home"
            >
        </label>
        <p class="description"><?php _e('أدخل كلاس الأيقونة من Font Awesome. مثال: fas fa-home', 'sekaiplus'); ?></p>
    </div>
    <?php
}

add_action('wp_nav_menu_item_custom_fields', 'sekaiplus_add_menu_icon_field', 10, 2);

/**
 * Save menu icon field
 */
function sekaiplus_save_menu_icon_field($menu_id, $menu_item_db_id) {
    if (isset($_POST['menu-item-icon'][$menu_item_db_id])) {
        $icon = sanitize_text_field($_POST['menu-item-icon'][$menu_item_db_id]);
        update_post_meta($menu_item_db_id, '_menu_item_icon', $icon);
    }
}
add_action('wp_update_nav_menu_item', 'sekaiplus_save_menu_icon_field', 10, 2);

/**
 * Add icon to menu items
 */
function sekaiplus_menu_item_title($title, $item, $args, $depth) {
    if (isset($args->theme_location) && $args->theme_location === 'primary') {
        $icon = get_post_meta($item->ID, '_menu_item_icon', true);
        if ($icon) {
            $title = '<i class="' . esc_attr($icon) . ' me-2"></i>' . $title;
        }
    }
    return $title;
}
add_filter('nav_menu_item_title', 'sekaiplus_menu_item_title', 10, 4);

/**
 * Get user unread notifications count
 */
function get_user_unread_notifications_count() {
    $user_id = get_current_user_id();
    if (!$user_id) return 0;

    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_user_notifications';

    $count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name
        WHERE user_id = %d AND is_read = 0",
        $user_id
    ));

    return (int) $count;
}


/**
 * AJAX handler for loading latest chapters
 */
function sekaiplus_load_latest_chapters() {
    check_ajax_referer('sekaiplus_latest_chapters', 'nonce');
    
    $page = isset($_POST['page']) ? absint($_POST['page']) : 1;
    $per_page = 10;
    $filter = isset($_POST['filter']) ? sanitize_text_field($_POST['filter']) : 'all';
    $current_user_id = get_current_user_id();
    
    $args = array(
        'post_type' => 'chapter',
        'posts_per_page' => $per_page,
        'paged' => $page,
        'orderby' => 'date',
        'order' => 'DESC'
    );
    
    // تطبيق الفلترة حسب الخيار المحدد
    switch ($filter) {
        case 'today':
            // فصول اليوم فقط
            $args['date_query'] = array(
                array(
                    'after' => date('Y-m-d 00:00:00', current_time('timestamp')),
                    'before' => date('Y-m-d 23:59:59', current_time('timestamp')),
                    'inclusive' => true,
                ),
            );
            break;
            
        case 'week':
            // فصول الأسبوع
            $args['date_query'] = array(
                array(
                    'after' => date('Y-m-d 00:00:00', strtotime('-7 days', current_time('timestamp'))),
                    'inclusive' => true,
                ),
            );
            break;
            
        case 'following':
            // الفصول الأكثر متابعة
            $args['meta_key'] = '_views_count';
            $args['orderby'] = 'meta_value_num';
            $args['order'] = 'DESC';
            break;
            
        case 'bookmarked':
            if (is_user_logged_in()) {
                // الفصول من الروايات المفضلة للمستخدم
                $bookmarked_novels = get_user_meta($current_user_id, '_novel_bookmarks', true);
                if (!empty($bookmarked_novels) && is_array($bookmarked_novels)) {
                    $args['meta_query'] = array(
                        array(
                            'key' => '_novel_id',
                            'value' => $bookmarked_novels,
                            'compare' => 'IN'
                        )
                    );
                } else {
                    // إذا لم يكن لدى المستخدم أي روايات في المفضلة، ارجع قائمة فارغة
                    wp_send_json_success(array());
                    return;
                }
            } else {
                // إذا لم يكن المستخدم مسجل الدخول
                wp_send_json_success(array());
                return;
            }
            break;
            
        default: // 'all' or any other value
            // لا تحتاج لتغييرات إضافية على الإستعلام
            break;
    }
    
    $chapters = get_posts($args);
    
    $formatted_chapters = array();
    
    foreach ($chapters as $chapter) {
        $novel_id = get_post_meta($chapter->ID, '_novel_id', true);
        if (empty($novel_id)) {
            $novel_id = $chapter->post_parent; // Fallback to post parent
        }
        $novel = get_post($novel_id);
        $translator = get_user_by('id', $chapter->post_author);
        
        // Get novel cover
        $novel_cover = '';
        if ($novel && has_post_thumbnail($novel->ID)) {
            $novel_cover = get_the_post_thumbnail_url($novel->ID, 'thumbnail');
        }
        
        // Get translator avatar
        $translator_avatar = get_avatar_url($translator->ID, array('size' => 24));
        
        $formatted_chapters[] = array(
            'chapter_title' => get_the_title($chapter),
            'chapter_link' => get_permalink($chapter),
            'novel_title' => $novel ? get_the_title($novel) : '',
            'novel_link' => $novel ? get_permalink($novel) : '',
            'novel_cover' => $novel_cover,
            'translator_name' => $translator->display_name,
            'translator_link' => get_author_posts_url($translator->ID),
            'translator_avatar' => $translator_avatar,
            'date' => get_the_date('c', $chapter)
        );
    }
    
    wp_send_json_success($formatted_chapters);
}
add_action('wp_ajax_sekaiplus_load_latest_chapters', 'sekaiplus_load_latest_chapters');
add_action('wp_ajax_nopriv_sekaiplus_load_latest_chapters', 'sekaiplus_load_latest_chapters');

/**
 * Add custom meta boxes for novels
 */
function sekaiplus_add_novel_meta_boxes() {
    add_meta_box(
        'novel_details',
        __('معلومات الرواية', 'sekaiplus'),
        'sekaiplus_novel_details_callback',
        'novel',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'sekaiplus_add_novel_meta_boxes');

/**
 * Novel details meta box callback
 */
function sekaiplus_novel_details_callback($post) {
    wp_nonce_field('sekaiplus_novel_details', 'novel_details_nonce');

    // Get current values
    $japanese_title = get_post_meta($post->ID, '_japanese_title', true);
    $romaji_title = get_post_meta($post->ID, '_romaji_title', true);
    $english_title = get_post_meta($post->ID, '_english_title', true);
    $release_date = get_post_meta($post->ID, '_release_date', true);
    $novel_type = get_post_meta($post->ID, '_novel_type', true);
    $mangaupdates_id = get_post_meta($post->ID, '_mangaupdates_id', true);
    $anilist_id = get_post_meta($post->ID, '_anilist_id', true);
    $volume_covers = get_post_meta($post->ID, '_volume_covers', true) ?: array();

    // Get current authors and illustrators
    $current_authors = wp_get_object_terms($post->ID, 'novel_author', array('fields' => 'ids'));
    $current_illustrators = wp_get_object_terms($post->ID, 'novel_illustrator', array('fields' => 'ids'));

    // Change main title label
    echo '<script>jQuery(document).ready(function($) { $("#title-prompt-text").text("العنوان العربي للرواية"); });</script>';

    // Titles
    echo '<div class="novel-meta-section">';
    echo '<h4>' . __('العناوين', 'sekaiplus') . '</h4>';
    echo '<p><label>' . __('العنوان الياباني', 'sekaiplus') . '</label>';
    echo '<input type="text" name="japanese_title" value="' . esc_attr($japanese_title) . '" class="widefat"></p>';
    
    echo '<p><label>' . __('العنوان الروماجي', 'sekaiplus') . '</label>';
    echo '<input type="text" name="romaji_title" value="' . esc_attr($romaji_title) . '" class="widefat"></p>';
    
    echo '<p><label>' . __('العنوان الإنجليزي', 'sekaiplus') . '</label>';
    echo '<input type="text" name="english_title" value="' . esc_attr($english_title) . '" class="widefat"></p>';
    echo '</div>';

    // Release Date
    echo '<div class="novel-meta-section">';
    echo '<h4>' . __('تاريخ الإصدار', 'sekaiplus') . '</h4>';
    echo '<input type="date" name="release_date" value="' . esc_attr($release_date) . '" class="widefat">';
    echo '</div>';

    // Novel Type
    echo '<div class="novel-meta-section">';
    echo '<h4>' . __('النوع', 'sekaiplus') . '</h4>';
    echo '<select name="novel_type" class="widefat">';
    echo '<option value="light_novel"' . selected($novel_type, 'light_novel', false) . '>' . __('رواية خفيفة', 'sekaiplus') . '</option>';
    echo '<option value="web_novel"' . selected($novel_type, 'web_novel', false) . '>' . __('رواية ويب', 'sekaiplus') . '</option>';
    echo '</select>';
    echo '</div>';

    // Author and Illustrator
    echo '<div class="novel-meta-section">';
    echo '<h4>' . __('المؤلف والرسام', 'sekaiplus') . '</h4>';
    
    // Author field
    echo '<div class="creator-field">';
    echo '<p><label>' . __('المؤلف', 'sekaiplus') . '</label>';
    $authors = get_terms(array(
        'taxonomy' => 'novel_author',
        'hide_empty' => false,
    ));
    echo '<select name="novel_author" class="widefat">';
    echo '<option value="">' . __('-- اختر المؤلف --', 'sekaiplus') . '</option>';
    foreach ($authors as $author) {
        echo '<option value="' . esc_attr($author->term_id) . '" ' . selected(in_array($author->term_id, $current_authors), true, false) . '>' . 
             esc_html($author->name) . '</option>';
    }
    echo '</select>';
    echo '<a href="#" class="add-new-creator" data-type="author">' . __('إضافة مؤلف جديد', 'sekaiplus') . '</a></p>';
    echo '</div>';
    
    // Illustrator field
    echo '<div class="creator-field">';
    echo '<p><label>' . __('الرسام', 'sekaiplus') . '</label>';
    $illustrators = get_terms(array(
        'taxonomy' => 'novel_illustrator',
        'hide_empty' => false,
    ));
    echo '<select name="novel_illustrator" class="widefat">';
    echo '<option value="">' . __('-- اختر الرسام --', 'sekaiplus') . '</option>';
    foreach ($illustrators as $illustrator) {
        echo '<option value="' . esc_attr($illustrator->term_id) . '" ' . selected(in_array($illustrator->term_id, $current_illustrators), true, false) . '>' . 
             esc_html($illustrator->name) . '</option>';
    }
    echo '</select>';
    echo '<a href="#" class="add-new-creator" data-type="illustrator">' . __('إضافة رسام جديد', 'sekaiplus') . '</a></p>';
    echo '</div>';
    echo '</div>';

    // Volume Covers
    echo '<div class="novel-meta-section">';
    echo '<h4>' . __('أغلفة المجلدات', 'sekaiplus') . '</h4>';
    echo '<div id="volume-covers-container">';
    foreach ($volume_covers as $index => $cover) {
        echo '<div class="volume-cover-item">';
        echo '<p><label>' . __('رقم المجلد', 'sekaiplus') . '</label>';
        echo '<input type="number" name="volume_numbers[]" value="' . esc_attr($cover['number']) . '" min="1"></p>';
        echo '<p><label>' . __('عنوان المجلد', 'sekaiplus') . '</label>';
        echo '<input type="text" name="volume_titles[]" value="' . esc_attr($cover['title']) . '"></p>';
        echo '<p><label>' . __('صورة الغلاف', 'sekaiplus') . '</label>';
        echo '<input type="hidden" name="volume_covers[]" value="' . esc_attr($cover['image']) . '" class="volume-cover-input">';
        echo '<button type="button" class="upload-volume-cover button">' . __('اختر صورة', 'sekaiplus') . '</button>';
        echo '<button type="button" class="remove-volume-cover button">' . __('حذف', 'sekaiplus') . '</button></p>';
        if ($cover['image']) {
            echo '<img src="' . esc_url($cover['image']) . '" class="volume-cover-preview">';
        }
        echo '</div>';
    }
    echo '</div>';
    echo '<button type="button" id="add-volume-cover" class="button">' . __('إضافة مجلد', 'sekaiplus') . '</button>';
    echo '</div>';

    // External Links
    echo '<div class="novel-meta-section">';
    echo '<h4>' . __('روابط خارجية', 'sekaiplus') . '</h4>';
    echo '<p><label>' . __('معرف MangaUpdates', 'sekaiplus') . '</label>';
    echo '<input type="text" name="mangaupdates_id" value="' . esc_attr($mangaupdates_id) . '" class="widefat" placeholder="مثال: klbld7a"></p>';
    
    echo '<p><label>' . __('معرف AniList', 'sekaiplus') . '</label>';
    echo '<input type="text" name="anilist_id" value="' . esc_attr($anilist_id) . '" class="widefat" placeholder="مثال: 31414"></p>';
    echo '</div>';

    // Add necessary scripts
    wp_enqueue_media();
    ?>
    <script>
    jQuery(document).ready(function($) {
        // Volume cover upload handling
        function handleMediaUpload(button) {
            var frame = wp.media({
                title: '<?php _e("اختر صورة الغلاف", "sekaiplus"); ?>',
                multiple: false
            });

            frame.on('select', function() {
                var attachment = frame.state().get('selection').first().toJSON();
                var container = $(button).closest('.volume-cover-item');
                container.find('.volume-cover-input').val(attachment.url);
                container.find('.volume-cover-preview').remove();
                container.append('<img src="' + attachment.url + '" class="volume-cover-preview">');
            });

            frame.open();
        }

        // Add new volume cover
        $('#add-volume-cover').on('click', function() {
            var template = `
                <div class="volume-cover-item">
                    <p><label><?php _e("رقم المجلد", "sekaiplus"); ?></label>
                    <input type="number" name="volume_numbers[]" min="1"></p>
                    <p><label><?php _e("عنوان المجلد", "sekaiplus"); ?></label>
                    <input type="text" name="volume_titles[]"></p>
                    <p><label><?php _e("صورة الغلاف", "sekaiplus"); ?></label>
                    <input type="hidden" name="volume_covers[]" class="volume-cover-input">
                    <button type="button" class="upload-volume-cover button"><?php _e("اختر صورة", "sekaiplus"); ?></button>
                    <button type="button" class="remove-volume-cover button"><?php _e("حذف", "sekaiplus"); ?></button></p>
                </div>
            `;
            $('#volume-covers-container').append(template);
        });

        // Handle volume cover upload
        $(document).on('click', '.upload-volume-cover', function() {
            handleMediaUpload(this);
        });

        // Remove volume cover
        $(document).on('click', '.remove-volume-cover', function() {
            $(this).closest('.volume-cover-item').remove();
        });

        // Handle adding new creator (author/illustrator)
        $('.add-new-creator').on('click', function(e) {
            e.preventDefault();
            var type = $(this).data('type');
            var name = prompt(type === 'author' ? 'أدخل اسم المؤلف الجديد:' : 'أدخل اسم الرسام الجديد:');
            
            if (name) {
                var data = {
                    action: 'add_novel_creator',
                    security: '<?php echo wp_create_nonce("add_novel_creator_nonce"); ?>',
                    creator_type: type,
                    creator_name: name
                };

                $.post(ajaxurl, data, function(response) {
                    if (response.success) {
                        var select = type === 'author' ? $('select[name="novel_author"]') : $('select[name="novel_illustrator"]');
                        select.append(new Option(name, response.data.term_id, true, true));
                    } else {
                        alert(response.data.message);
                    }
                });
            }
        });
    });
    </script>
    <?php
}

/**
 * Save novel meta box data
 */
function sekaiplus_save_novel_meta($post_id) {
    if (!isset($_POST['novel_details_nonce']) || !wp_verify_nonce($_POST['novel_details_nonce'], 'sekaiplus_novel_details')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Save titles
    update_post_meta($post_id, '_japanese_title', sanitize_text_field($_POST['japanese_title']));
    update_post_meta($post_id, '_romaji_title', sanitize_text_field($_POST['romaji_title']));
    update_post_meta($post_id, '_english_title', sanitize_text_field($_POST['english_title']));

    // Save release date
    update_post_meta($post_id, '_release_date', sanitize_text_field($_POST['release_date']));

    // Save novel type
    update_post_meta($post_id, '_novel_type', sanitize_text_field($_POST['novel_type']));

    // Save author
    if (isset($_POST['novel_author'])) {
        $author_id = intval($_POST['novel_author']);
        if ($author_id > 0) {
            wp_set_object_terms($post_id, $author_id, 'novel_author');
        } else {
            wp_delete_object_term_relationships($post_id, 'novel_author');
        }
    }

    // Save illustrator
    if (isset($_POST['novel_illustrator'])) {
        $illustrator_id = intval($_POST['novel_illustrator']);
        if ($illustrator_id > 0) {
            wp_set_object_terms($post_id, $illustrator_id, 'novel_illustrator');
        } else {
            wp_delete_object_term_relationships($post_id, 'novel_illustrator');
        }
    }

    // Save volume covers
    $volume_covers = array();
    if (isset($_POST['volume_numbers']) && is_array($_POST['volume_numbers'])) {
        for ($i = 0; $i < count($_POST['volume_numbers']); $i++) {
            if (!empty($_POST['volume_numbers'][$i])) {
                $volume_covers[] = array(
                    'number' => intval($_POST['volume_numbers'][$i]),
                    'title' => sanitize_text_field($_POST['volume_titles'][$i]),
                    'image' => esc_url_raw($_POST['volume_covers'][$i])
                );
            }
        }
    }
    update_post_meta($post_id, '_volume_covers', $volume_covers);

    // Save external links
    update_post_meta($post_id, '_mangaupdates_id', sanitize_text_field($_POST['mangaupdates_id']));
    update_post_meta($post_id, '_anilist_id', sanitize_text_field($_POST['anilist_id']));
}
add_action('save_post_novel', 'sekaiplus_save_novel_meta');

/**
 * AJAX handler for adding new creators
 */
function sekaiplus_add_novel_creator() {
    check_ajax_referer('add_novel_creator_nonce', 'security');

    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => __('غير مصرح لك بإضافة مؤلفين أو رسامين.', 'sekaiplus')));
    }

    $creator_type = sanitize_text_field($_POST['creator_type']);
    $creator_name = sanitize_text_field($_POST['creator_name']);
    $taxonomy = $creator_type === 'author' ? 'novel_author' : 'novel_illustrator';

    $term = wp_insert_term($creator_name, $taxonomy);

    if (is_wp_error($term)) {
        wp_send_json_error(array('message' => $term->get_error_message()));
    } else {
        wp_send_json_success(array('term_id' => $term['term_id']));
    }
}
add_action('wp_ajax_add_novel_creator', 'sekaiplus_add_novel_creator');

/**
 * Modify permalinks for novels and chapters to use query parameters
 */
function sekaiplus_modify_permalinks($url, $post) {
    if ($post->post_type === 'chapter') {
        $chapter_id = get_post_meta($post->ID, '_chapter_unique_id', true);
        if (!empty($chapter_id)) {
            $author_id = $post->post_author;
            return rtrim(home_url(), '/') . '/chapter?r=' . $chapter_id . '&author=' . $author_id;
        }
    } elseif ($post->post_type === 'novel') {
        $novel_id = get_post_meta($post->ID, '_novel_unique_id', true);
        if (!empty($novel_id)) {
            return rtrim(home_url(), '/') . '/novel?r=' . $novel_id;
        }
    }
    return $url;
}
add_filter('post_type_link', 'sekaiplus_modify_permalinks', 10, 2);

/**
 * Add rewrite rules for novels and chapters
 */
function sekaiplus_add_custom_rewrite_rules() {
    add_rewrite_rule(
        'novel$',
        'index.php?post_type=novel',
        'top'
    );
    add_rewrite_rule(
        'chapter$',
        'index.php?post_type=chapter',
        'top'
    );

    add_rewrite_rule(
        'chapter\?r=([^&]+)&author=([^&]+)$',
        'index.php?post_type=chapter&r=$matches[1]&author=$matches[2]',
        'top'
    );
    add_rewrite_rule(
        'chapter\?r=([^&]+)$',
        'index.php?post_type=chapter&r=$matches[1]',
        'top'
    );
    add_rewrite_rule(
        'novel\?r=([^&]+)$',
        'index.php?post_type=novel&r=$matches[1]',
        'top'
    );
}
add_action('init', 'sekaiplus_add_custom_rewrite_rules', 10);

/**
 * إضافة متغيرات الاستعلام
 */
function sekaiplus_add_query_vars($vars) {
    $vars[] = 'r';
    $vars[] = 'author';
    return $vars;
}
add_filter('query_vars', 'sekaiplus_add_query_vars');

/**
 * Handle novel and chapter requests
 */
function sekaiplus_handle_custom_requests() {
    global $wp_query;

    if (is_admin()) {
        return;
    }

    $post_type = get_query_var('post_type');
    $request_id = get_query_var('r');
    $author = get_query_var('author');

    if (empty($request_id)) {
        return;
    }

    if ($post_type === 'novel') {
        $novels = get_posts(array(
            'post_type' => 'novel',
            'meta_key' => '_novel_unique_id',
            'meta_value' => $request_id,
            'posts_per_page' => 1
        ));

        if (!empty($novels)) {
            $wp_query->query(array(
                'p' => $novels[0]->ID,
                'post_type' => 'novel'
            ));
            return;
        }
    } elseif ($post_type === 'chapter') {
        $query_args = array(
            'post_type' => 'chapter',
            'meta_key' => '_chapter_unique_id',
            'meta_value' => $request_id,
            'posts_per_page' => -1
        );
        
        if (!empty($author)) {
            $translator_user = get_user_by('id', $author);
            if ($translator_user) {
                $query_args['author'] = $author;
                $query_args['posts_per_page'] = 1;
            }
        }
        
        $chapters = get_posts($query_args);

        if (!empty($chapters)) {
            if (!empty($author) && count($chapters) == 1) {
                $wp_query->query(array(
                    'p' => $chapters[0]->ID,
                    'post_type' => 'chapter'
                ));
                return;
            } elseif (empty($author)) {
                $wp_query->query(array(
                    'p' => $chapters[0]->ID,
                    'post_type' => 'chapter'
                ));
                return;
            } else {
                $preferred_translator = '';
                if (isset($_COOKIE['preferred_translator'])) {
                    $preferred_translator = $_COOKIE['preferred_translator'];
                }
                
                if (!empty($preferred_translator)) {
                    foreach ($chapters as $chapter) {
                        $chapter_translator = get_the_author_meta('user_nicename', $chapter->post_author);
                        if ($chapter_translator === $preferred_translator) {
                            $wp_query->query(array(
                                'p' => $chapter->ID,
                                'post_type' => 'chapter'
                            ));
                            return;
                        }
                    }
                }
                
                $wp_query->query(array(
                    'p' => $chapters[0]->ID,
                    'post_type' => 'chapter'
                ));
                return;
            }
        }
    }

    if (empty($novels) && empty($chapters)) {
        $wp_query->set_404();
        status_header(404);
    }
}
add_action('template_redirect', 'sekaiplus_handle_custom_requests', 5);

/**
 * Generate unique ID for novels
 */
function sekaiplus_generate_unique_id($length = 11) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $id = '';
    for ($i = 0; $i < $length; $i++) {
        $id .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $id;
}

/**
 * Add unique ID when creating a new novel
 */
function sekaiplus_set_novel_unique_id($post_id, $post, $update) {
    if ($post->post_type !== 'novel') {
        return;
    }

    $unique_id = get_post_meta($post_id, '_novel_unique_id', true);
    if (empty($unique_id)) {
        do {
            $unique_id = sekaiplus_generate_unique_id();
            $existing_posts = get_posts(array(
                'post_type' => 'novel',
                'meta_key' => '_novel_unique_id',
                'meta_value' => $unique_id,
                'posts_per_page' => 1
            ));
        } while (!empty($existing_posts));

        update_post_meta($post_id, '_novel_unique_id', $unique_id);
    }
}
add_action('wp_insert_post', 'sekaiplus_set_novel_unique_id', 10, 3);

/**
 * إنشاء معرف فريد للفصول
 */
function sekaiplus_generate_chapter_id($length = 11) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $id = '';
    for ($i = 0; $i < $length; $i++) {
        $id .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $id;
}

/**
 * إضافة معرف فريد للفصول عند إنشائها
 */
function sekaiplus_add_chapter_id($post_id) {
    if (get_post_type($post_id) === 'chapter') {
        $chapter_id = get_post_meta($post_id, '_chapter_unique_id', true);
        if (empty($chapter_id)) {
            do {
                $chapter_id = sekaiplus_generate_chapter_id();
            } while (sekaiplus_chapter_id_exists($chapter_id, $post_id));
            
            update_post_meta($post_id, '_chapter_unique_id', $chapter_id);
        }
    }
}
add_action('wp_insert_post', 'sekaiplus_add_chapter_id');
add_action('save_post', 'sekaiplus_add_chapter_id');

/**
 * التحقق من وجود المعرف الفريد
 */
function sekaiplus_chapter_id_exists($chapter_id, $exclude_post_id = null) {
    global $wpdb;
    $query = $wpdb->prepare(
        "SELECT post_id FROM {$wpdb->postmeta} 
        WHERE meta_key = '_chapter_unique_id' 
        AND meta_value = %s",
        $chapter_id
    );
    
    if ($exclude_post_id) {
        $query .= $wpdb->prepare(" AND post_id != %d", $exclude_post_id);
    }
    
    return $wpdb->get_var($query) ? true : false;
}

/**
 * إعادة توجيه الفصول باستخدام المعرف الفريد
 */
function sekaiplus_redirect_chapter() {
    if (!is_singular('chapter')) {
        return;
    }

    if (isset($_GET['r'])) {
        return;
    }

    global $post;
    $unique_id = get_post_meta($post->ID, '_chapter_unique_id', true);
    
    if (!empty($unique_id)) {
        $translator = get_the_author_meta('user_nicename', $post->post_author);
        
        if (current_user_can('manage_options')) {
            setcookie('debug_unique_id', $unique_id, time() + 3600, '/');
            setcookie('debug_translator', $translator, time() + 3600, '/');
            setcookie('debug_cookie', isset($_COOKIE['preferred_translator']) ? $_COOKIE['preferred_translator'] : 'none', time() + 3600, '/');
        }
        
        wp_redirect(home_url("chapter/{$unique_id}/{$translator}"), 301);
        exit;
    }
}
add_action('template_redirect', 'sekaiplus_redirect_chapter', 1);

/**
 * Add custom meta boxes for chapters
 */
function sekaiplus_add_chapter_meta_boxes() {
    add_meta_box(
        'chapter_details',
        __('تفاصيل الفصل', 'sekaiplus'),
        'sekaiplus_chapter_details_callback',
        'chapter',
        'normal',
        'high'
    );
    
    add_meta_box(
        'chapter_unique_id',
        __('المعرف الفريد للفصل', 'sekaiplus'),
        'sekaiplus_chapter_unique_id_callback',
        'chapter',
        'side',
        'high'
    );
}
add_action('add_meta_boxes', 'sekaiplus_add_chapter_meta_boxes');

/**
 * Chapter details meta box callback
 */
function sekaiplus_chapter_details_callback($post) {
    wp_nonce_field('sekaiplus_chapter_details', 'chapter_details_nonce');

    $novel_id = get_post_meta($post->ID, '_novel_id', true);
    $volume_number = get_post_meta($post->ID, '_volume_number', true);
    $chapter_number = get_post_meta($post->ID, '_chapter_number', true);

    $novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC'
    ));
    ?>
    <div class="chapter-meta-box">
        <div class="meta-row">
            <label for="novel_id"><?php _e('الرواية:', 'sekaiplus'); ?></label>
            <select name="novel_id" id="novel_id" class="widefat" required>
                <option value=""><?php _e('اختر الرواية...', 'sekaiplus'); ?></option>
                <?php foreach ($novels as $novel) : ?>
                    <option value="<?php echo $novel->ID; ?>" <?php selected($novel_id, $novel->ID); ?>>
                        <?php echo $novel->post_title; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="meta-row">
            <label for="volume_number"><?php _e('رقم المجلد:', 'sekaiplus'); ?></label>
            <input type="number" id="volume_number" name="volume_number" 
                   value="<?php echo esc_attr($volume_number); ?>" min="1" required>
        </div>

        <div class="meta-row">
    <label for="chapter_number"><?php _e('رقم الفصل:', 'sekaiplus'); ?></label>
    <input type="number" id="chapter_number" name="chapter_number" 
           value="<?php echo esc_attr($chapter_number); ?>" min="-1" required>
    <p class="description">
        <?php _e('استخدم -1 للرسوم التوضيحية، 0 للمقدمة', 'sekaiplus'); ?>
    </p>
</div>

        <div class="meta-row illustration-checkbox">
    <label for="is_illustration">
        <input type="checkbox" id="is_illustration" name="is_illustration" value="1" <?php checked(get_post_meta($post->ID, '_is_illustration', true), '1'); ?>>
        <?php _e('فصل الرسوم التوضيحية', 'sekaiplus'); ?>
    </label>
    <p class="description">
        <?php _e('اختر هذا الخيار إذا كان هذا الفصل يحتوي على رسوم توضيحية فقط للمجلد. سيتم عرضه قبل المقدمة وجميع الفصول الأخرى.', 'sekaiplus'); ?>
    </p>
</div>

    </div>

    <style>

.chapter-meta-box .illustration-checkbox label {
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}
.chapter-meta-box .illustration-checkbox input {
    margin: 0;
}
        .chapter-meta-box .meta-row {
            margin-bottom: 1rem;
        }
        .chapter-meta-box label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        .chapter-meta-box .description {
            margin-top: 0.25rem;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
    </style>
    
    </style>

    <script type="text/javascript">
jQuery(document).ready(function($) {
    // عند تحميل الصفحة
    function toggleFields() {
    if ($('#is_illustration').is(':checked')) {
        // عند اختيار الرسوم التوضيحية، تعيين رقم الفصل إلى -1
        $('#chapter_number').val('-1');
        $('#title').val('رسوم توضيحية').prop('readonly', true).css('background-color', '#f0f0f0');
    } else {
        // عند إلغاء الاختيار، إفراغ الحقل ليملأه المستخدم
        if ($('#chapter_number').val() === '-1') {
            $('#chapter_number').val('');
        }
        $('#title').prop('readonly', false).css('background-color', '');
    }
}
    
    // تنفيذ الدالة عند تحميل الصفحة
    toggleFields();
    
    // عند تغيير حالة خانة الاختيار
    $('#is_illustration').on('change', function() {
        toggleFields();
    });
});
</script>
<?php
}

/**
 * Chapter unique ID meta box callback
 */
function sekaiplus_chapter_unique_id_callback($post) {
    $chapter_unique_id = get_post_meta($post->ID, '_chapter_unique_id', true);
    if (empty($chapter_unique_id)) {
        $chapter_unique_id = sekaiplus_generate_chapter_id();
        update_post_meta($post->ID, '_chapter_unique_id', $chapter_unique_id);
    }
    
    echo '<div class="chapter-unique-id">';
    echo '<p><strong>المعرف الفريد:</strong> ' . esc_html($chapter_unique_id) . '</p>';
    
    $translations = sekaiplus_get_chapter_translations($post->ID);
    if (count($translations) > 1) {
        echo '<p><strong>الترجمات المتوفرة:</strong></p>';
        echo '<ul class="translations-list">';
        foreach ($translations as $translation) {
            if ($translation['translator_id'] != $post->post_author) {
                echo sprintf(
                    '<li><a href="%s">%s</a> - %s</li>',
                    esc_url(get_edit_post_link($translation['chapter_id'])),
                    esc_html($translation['translator_name']),
                    esc_html($translation['date'])
                );
            }
        }
        echo '</ul>';
    }
    echo '</div>';
    
    echo '<style>
        .chapter-unique-id {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .chapter-unique-id p {
            margin: 0 0 10px 0;
        }
        .translations-list {
            margin: 0;
            padding-right: 20px;
        }
        .translations-list li {
            margin-bottom: 5px;
        }
    </style>';
}

/**
 * Save chapter meta box data
 */
function sekaiplus_save_chapter_details($post_id) {
    if (!isset($_POST['chapter_details_nonce']) || 
        !wp_verify_nonce($_POST['chapter_details_nonce'], 'sekaiplus_chapter_details')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    if ('chapter' !== get_post_type($post_id)) {
        return;
    }

    $fields = array(
        'novel_id' => 'intval',
        'volume_number' => 'intval',
        'chapter_number' => 'intval'
    );

    foreach ($fields as $field => $sanitize_callback) {
        if (isset($_POST[$field])) {
            $value = call_user_func($sanitize_callback, $_POST[$field]);
            update_post_meta($post_id, '_' . $field, $value);
        }
    }
    
    // Handle the illustration checkbox
    $is_illustration = isset($_POST['is_illustration']) ? 1 : 0;
    update_post_meta($post_id, '_is_illustration', $is_illustration);
    
// إذا كان فصل رسوم توضيحية، لا تقم بتحديث العنوان هنا لتجنب التعارض
if ($is_illustration == 1) {
    // فقط تحديث رقم الفصل إلى -1
    update_post_meta($post_id, '_chapter_number', -1);
    // لا نستخدم wp_update_post هنا لتفادي المشكلة
}
}
add_action('save_post', 'sekaiplus_save_chapter_details');

/**
 * Add custom columns in chapter list
 */
function sekaiplus_add_chapter_columns($columns) {
    $new_columns = array();
    foreach ($columns as $key => $value) {
        if ($key === 'title') {
            $new_columns[$key] = $value;
            $new_columns['novel'] = __('الرواية', 'sekaiplus');
            $new_columns['volume'] = __('المجلد', 'sekaiplus');
            $new_columns['chapter'] = __('الفصل', 'sekaiplus');
            $new_columns['translator'] = __('المترجم', 'sekaiplus');
        } else {
            $new_columns[$key] = $value;
        }
    }
    return $new_columns;
}
add_filter('manage_chapter_posts_columns', 'sekaiplus_add_chapter_columns');

function sekaiplus_chapter_column_content($column, $post_id) {
    switch ($column) {
        case 'novel':
            $novel_id = get_post_meta($post_id, '_novel_id', true);
            if ($novel_id) {
                $novel = get_post($novel_id);
                if ($novel) {
                    echo '<a href="' . get_edit_post_link($novel_id) . '">' . $novel->post_title . '</a>';
                }
            }
            break;
        
        case 'volume':
            $volume_number = get_post_meta($post_id, '_volume_number', true);
            echo $volume_number ? $volume_number : '-';
            break;
            
        case 'chapter':
            $chapter_number = get_post_meta($post_id, '_chapter_number', true);
            if ($chapter_number == -1) {
                echo 'الرسوم التوضيحية';
            } elseif ($chapter_number == 0) {
                echo 'المقدمة';
            } else {
                echo $chapter_number;
            }
            break;
            
        case 'translator':
            $author_id = get_post_field('post_author', $post_id);
            $author = get_userdata($author_id);
            if ($author) {
                echo '<a href="' . get_edit_user_link($author_id) . '">' . $author->display_name . '</a>';
            }
            break;
    }
}
add_action('manage_chapter_posts_custom_column', 'sekaiplus_chapter_column_content', 10, 2);

/**
 * Add filters for sorting in chapter list
 */
function sekaiplus_add_chapter_filters() {
    global $typenow;
    if ($typenow === 'chapter') {
        $novel_id = isset($_GET['novel_id']) ? $_GET['novel_id'] : '';
        $novels = get_posts(array(
            'post_type' => 'novel',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        ?>
        <select name="novel_id">
            <option value=""><?php _e('كل الروايات', 'sekaiplus'); ?></option>
            <?php foreach ($novels as $novel) : ?>
                <option value="<?php echo $novel->ID; ?>" <?php selected($novel_id, $novel->ID); ?>>
                    <?php echo $novel->post_title; ?>
                </option>
            <?php endforeach; ?>
        </select>
        <?php
    }
}
add_action('restrict_manage_posts', 'sekaiplus_add_chapter_filters');

/**
 * Apply novel filter
 */
function sekaiplus_filter_chapters_by_novel($query) {
    global $pagenow;
    if (is_admin() && $pagenow === 'edit.php' && 
        isset($_GET['post_type']) && $_GET['post_type'] === 'chapter' && 
        isset($_GET['novel_id']) && !empty($_GET['novel_id'])) {
        
        $query->query_vars['meta_key'] = '_novel_id';
        $query->query_vars['meta_value'] = intval($_GET['novel_id']);
    }
}
add_action('pre_get_posts', 'sekaiplus_filter_chapters_by_novel');

/**
 * Modify chapter title in chapter list
 */
function sekaiplus_change_chapter_title_placeholder($title_placeholder) {
    global $post;
    if (isset($post) && 'chapter' === $post->post_type) {
        $title_placeholder = 'عنوان الفصل';
    }
    return $title_placeholder;
}
add_filter('enter_title_here', 'sekaiplus_change_chapter_title_placeholder');

/**
 * Modify chapter title in chapter list
 */
function sekaiplus_modify_chapter_title_in_list($title, $post_id) {
    if (get_post_type($post_id) === 'chapter') {
        $chapter_number = get_post_meta($post_id, '_chapter_number', true);
        $volume_number = get_post_meta($post_id, '_volume_number', true);
        $is_illustration = get_post_meta($post_id, '_is_illustration', true);
        
        if ($is_illustration == '1') {
            return sprintf('المجلد %d - الرسوم التوضيحية', $volume_number);
        } else if ($chapter_number === '0') {
            return sprintf('المجلد %d - المقدمة', $volume_number);
        }
    }
    return $title;
}
add_filter('the_title', 'sekaiplus_modify_chapter_title_in_list', 10, 2);

/**
 * Get chapter order value for sorting
 */
function sekaiplus_get_chapter_order_value($post_id) {
    $is_illustration = get_post_meta($post_id, '_is_illustration', true);
    $chapter_number = get_post_meta($post_id, '_chapter_number', true);
    
    if ($is_illustration == '1') {
        // يعطي فصول الرسوم التوضيحية ترتيباً سالباً ليظهر قبل المقدمة (0) وكل الفصول
        return -1;
    }
    
    return $chapter_number;
}

/**
 * Add CSS for displaying intro chapters
 */
function sekaiplus_admin_chapter_styles() {
    ?>
    <style>
        
        /* Styling for illustration chapter */
.widefat .column-chapter_number span.illustration {
    background-color: #fef7ea;
    border: 1px solid #d98500;
    color: #d98500;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

/* Add illustration icon */
.widefat .column-chapter_number span.illustration:before {
    content: "\f161"; /* Dashicons: image */
    font-family: dashicons;
    margin-right: 5px;
    vertical-align: middle;
}

        .chapter-intro {
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.9em;
            color: #495057;
        }
    </style>
    <?php
}
add_action('admin_head', 'sekaiplus_admin_chapter_styles');

/**
 * Register volume post type
 */
function sekaiplus_register_volume_post_type() {
    $labels = array(
        'name'               => 'المجلدات',
        'singular_name'      => 'مجلد',
        'menu_name'          => 'المجلدات',
        'add_new'            => 'إضافة مجلد جديد',
        'add_new_item'       => 'إضافة مجلد جديد',
        'edit_item'          => 'تعديل المجلد',
        'new_item'           => 'مجلد جديد',
        'view_item'          => 'عرض المجلد',
        'search_items'       => 'بحث في المجلدات',
        'not_found'          => 'لم يتم العثور على مجلدات',
        'not_found_in_trash' => 'لا توجد مجلدات في سلة المهملات'
    );

    $args = array(
        'labels'              => $labels,
        'public'              => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'query_var'           => true,
        'rewrite'             => array('slug' => 'volume'),
        'capability_type'     => 'post',
        'has_archive'         => false,
        'hierarchical'        => false,
        'menu_position'       => 5,
        'supports'            => array('title', 'editor', 'thumbnail'),
        'menu_icon'           => 'dashicons-book-alt'
    );

    register_post_type('volume', $args);
}
add_action('init', 'sekaiplus_register_volume_post_type');

/**
 * Save volume meta data
 */
function sekaiplus_save_volume_meta($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (!current_user_can('edit_post', $post_id)) return;
    if (get_post_type($post_id) !== 'volume') return;

    if (isset($_POST['_volume_number'])) {
        update_post_meta($post_id, '_volume_number', sanitize_text_field($_POST['_volume_number']));
    }

    if (isset($_POST['_novel_id'])) {
        update_post_meta($post_id, '_novel_id', sanitize_text_field($_POST['_novel_id']));
    }

    if (isset($_POST['_volume_cover'])) {
        update_post_meta($post_id, '_volume_cover', esc_url_raw($_POST['_volume_cover']));
    }
}
add_action('save_post', 'sekaiplus_save_volume_meta');

/**
 * Add volume meta boxes
 */
function sekaiplus_add_volume_meta_boxes() {
    add_meta_box(
        'volume_details',
        'تفاصيل المجلد',
        'sekaiplus_volume_meta_box_callback',
        'volume',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'sekaiplus_add_volume_meta_boxes');

function sekaiplus_volume_meta_box_callback($post) {
    wp_nonce_field('sekaiplus_volume_meta_box', 'sekaiplus_volume_meta_box_nonce');

    $volume_number = get_post_meta($post->ID, '_volume_number', true);
    $novel_id = get_post_meta($post->ID, '_novel_id', true);
    $volume_cover = get_post_meta($post->ID, '_volume_cover', true);
    ?>
    <p>
        <label for="volume_number">رقم المجلد:</label>
        <input type="number" id="volume_number" name="_volume_number" value="<?php echo esc_attr($volume_number); ?>" min="1" step="1">
    </p>
    <p>
        <label for="novel_id">معرف الرواية:</label>
        <input type="number" id="novel_id" name="_novel_id" value="<?php echo esc_attr($novel_id); ?>" min="1">
    </p>
    <p>
        <label for="volume_cover">رابط غلاف المجلد:</label>
        <input type="url" id="volume_cover" name="_volume_cover" value="<?php echo esc_url($volume_cover); ?>" style="width: 100%;">
    </p>
    <?php
}

/**
 * Handle volume display in novel URL
 */
function sekaiplus_handle_volume_display($template) {
    global $wp_query, $wp, $post;
    
    if (!isset($_GET['r'])) {
        return $template;
    }

    $request = $_GET['r'];
    if (strpos($request, '/') === false) {
        return $template;
    }

    list($novel_id, $volume_number) = explode('/', $request);
    if (!is_numeric($volume_number)) {
        return $template;
    }

    $novels = get_posts(array(
        'post_type' => 'novel',
        'meta_key' => '_novel_unique_id',
        'meta_value' => $novel_id,
        'posts_per_page' => 1
    ));

    if (empty($novels)) {
        if (!is_null($wp_query)) {
            $wp_query->set_404();
            status_header(404);
            return get_404_template();
        }
        return $template;
    }

    $novel = $novels[0];
    
    $chapters = get_posts(array(
        'post_type' => 'chapter',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'value' => $novel->ID
            ),
            array(
                'key' => '_volume_number',
                'value' => $volume_number
            ),
            'chapter_order' => array(
                'key' => '_is_illustration',
                'compare' => 'EXISTS',
            ),
        ),
        'orderby' => array(
            'chapter_order' => 'DESC',
            'meta_value_num' => 'ASC',
        ),
        'meta_key' => '_chapter_number'
    ));

    if (empty($chapters)) {
        if (!is_null($wp_query)) {
            $wp_query->set_404();
            status_header(404);
            return get_404_template();
        }
        return $template;
    }

    $GLOBALS['current_novel'] = $novel;
    $GLOBALS['current_volume'] = array(
        'number' => $volume_number,
        'chapters' => $chapters
    );

    if (!is_null($wp_query)) {
        $wp_query->is_404 = false;
        $wp_query->is_page = true;
        $wp_query->is_singular = true;
        $wp_query->is_single = false;
        $wp_query->is_archive = false;
        $wp_query->is_home = false;

        $wp_query->posts = array($novel);
        $wp_query->post = $novel;
        $wp_query->post_count = 1;
        $wp_query->found_posts = 1;
        $wp_query->max_num_pages = 1;
        $wp_query->queried_object = $novel;
        $wp_query->queried_object_id = $novel->ID;
    }

    return get_template_directory() . '/single-volume.php';
}
add_filter('template_include', 'sekaiplus_handle_volume_display', 99);

/**
 * Modify author URLs to use u/username format
 */
function sekaiplus_author_base_rewrite_rules() {
    global $wp_rewrite;
    $wp_rewrite->author_base = 'u';
    $wp_rewrite->author_structure = '/' . $wp_rewrite->author_base . '/%author%';
}
add_action('init', 'sekaiplus_author_base_rewrite_rules');

/**
 * Modify author link to use u/username format
 */
function sekaiplus_author_link($link, $author_id) {
    return home_url('u/' . get_the_author_meta('user_nicename', $author_id));
}
add_filter('author_link', 'sekaiplus_author_link', 10, 2);

/**
 * Add AJAX handler for profile updates
 */
add_action('wp_ajax_update_profile', 'handle_profile_update');
function handle_profile_update() {
    if (!check_ajax_referer('profile_update_nonce', 'nonce', false)) {
        wp_send_json_error(array('message' => 'فشل التحقق من الأمان'));
        return;
    }

    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error(array('message' => 'يجب تسجيل الدخول أولاً'));
        return;
    }

    $display_name = sanitize_text_field($_POST['display_name']);
    $email = sanitize_email($_POST['email']);
    $bio = sanitize_textarea_field($_POST['bio']);

    if (!is_email($email)) {
        wp_send_json_error(array('message' => 'البريد الإلكتروني غير صالح'));
        return;
    }

    if (email_exists($email) && email_exists($email) != $user_id) {
        wp_send_json_error(array('message' => 'البريد الإلكتروني مستخدم بالفعل'));
        return;
    }

    $updated = wp_update_user(array(
        'ID' => $user_id,
        'display_name' => $display_name,
        'user_email' => $email
    ));

    if (is_wp_error($updated)) {
        wp_send_json_error(array('message' => $updated->get_error_message()));
        return;
    }

    update_user_meta($user_id, 'description', $bio);

    wp_send_json_success(array('message' => 'تم تحديث الملف الشخصي بنجاح'));
}

/**
 * الحصول على محتوى الفصل بناءً على المعرف الفريد والمترجم
 */
function sekaiplus_get_chapter_content_by_id($unique_id, $translator_id = null) {
    if (empty($unique_id)) {
        return false;
    }

    $args = array(
        'post_type' => 'chapter',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_chapter_unique_id',
                'value' => $unique_id,
                'compare' => '='
            )
        )
    );

    if (!empty($translator_id)) {
        $args['author'] = $translator_id;
    }

    $chapters = get_posts($args);

    if (!empty($chapters)) {
        if (!empty($translator_id)) {
            return $chapters[0];
        }
        
        return $chapters;
    }

    return false;
}

add_filter('the_content', function($content) {
    if (get_post_type() !== 'chapter') {
        return $content;
    }

    $requested_chapter_id = get_query_var('r', '');
    if (empty($requested_chapter_id)) {
        return $content;
    }

    $current_translator = get_post_field('post_author', get_the_ID());
    
    $chapter = sekaiplus_get_chapter_content_by_id($requested_chapter_id, $current_translator);
    
    if ($chapter) {
        return wpautop($chapter->post_content);
    }
    
    $other_translations = sekaiplus_get_chapter_translations($chapter->ID);
    if ($other_translations) {
        $output = '<div class="alert alert-info">هذا الفصل غير متوفر بترجمة ' . get_the_author() . '</div>';
        $output .= '<div class="mt-3"><strong>الترجمات المتوفرة:</strong></div>';
        $output .= '<div class="list-group mt-2">';
        foreach ($other_translations as $translation) {
            $translator = get_user_by('id', $translation['translator_id']);
            $output .= sprintf(
                '<li><a href="%s">%s</a> - %s</li>',
                esc_url(get_edit_post_link($translation['chapter_id'])),
                esc_html($translator->display_name),
                esc_html($translation['date'])
            );
        }
        $output .= '</div>';
        return $output;
    }
    
    return '<div class="alert alert-warning">عذراً، لا يمكن العثور على هذا الفصل.</div>';
}, 20);

/**
 * معالجة محتوى الفصل وتنسيق التعليقات التوضيحية
 */
function sekaiplus_process_chapter_content($content) {
    if (get_post_type() !== 'chapter') {
        return $content;
    }

    $content = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    $pattern = '/<span\s+class="translator-note"\s+data-note="([^"]+)">(.*?)<\/span>/si';
    $content = preg_replace_callback($pattern, function($matches) {
        $note = htmlspecialchars_decode($matches[1]);
        $text = $matches[2];
        return sprintf(
            '<span class="translator-note" data-note="%s">%s</span>',
            htmlspecialchars($note),
            $text
        );
    }, $content);
    
    return $content;
}

/**
 * تعطيل الذاكرة المؤقتة للفصول
 */
function sekaiplus_disable_chapter_cache($post_id) {
    if (get_post_type($post_id) === 'chapter') {
        wp_cache_delete($post_id, 'posts');
        wp_cache_delete($post_id, 'post_' . $post_id . '_content');
    }
}
add_action('save_post', 'sekaiplus_disable_chapter_cache');
add_action('edit_post', 'sekaiplus_disable_chapter_cache');
add_action('delete_post', 'sekaiplus_disable_chapter_cache');

/**
 * زيادة عدد مشاهدات الفصل (النظام القديم - محفوظ للتوافق)
 */
function sekaiplus_increment_chapter_views($chapter_id) {
    $views = (int)get_post_meta($chapter_id, '_views_count', true);
    update_post_meta($chapter_id, '_views_count', $views + 1);
}

/**
 * نظام ذكي ودقيق لحساب مشاهدات الفصول
 * يمنع المشاهدات المكررة ويستبعد المسؤولين والمحررين
 */
function sekaiplus_smart_increment_chapter_views($chapter_id) {
    // التحقق من صحة معرف الفصل
    if (!$chapter_id || !is_numeric($chapter_id)) {
        return false;
    }

    // التحقق من أن المنشور موجود ومن نوع chapter
    $post = get_post($chapter_id);
    if (!$post || $post->post_type !== 'chapter') {
        return false;
    }

    // استبعاد المسؤولين والمحررين والمترجمين من حساب المشاهدات
    if (current_user_can('edit_posts') || current_user_can('manage_options')) {
        return false;
    }

    // الحصول على معرف المستخدم أو IP للزوار غير المسجلين
    $user_identifier = is_user_logged_in() ? get_current_user_id() : sekaiplus_get_visitor_ip();

    // إنشاء مفتاح فريد للمشاهدة
    $view_key = 'chapter_view_' . $chapter_id . '_' . md5($user_identifier);

    // التحقق من وجود مشاهدة سابقة خلال آخر 30 دقيقة
    $last_view_time = get_transient($view_key);
    if ($last_view_time !== false) {
        return false; // المستخدم شاهد هذا الفصل مؤخراً
    }

    // تسجيل المشاهدة الجديدة
    set_transient($view_key, time(), 30 * MINUTE_IN_SECONDS); // منع المشاهدات المكررة لمدة 30 دقيقة

    // زيادة عدد المشاهدات
    $current_views = (int)get_post_meta($chapter_id, '_views_count', true);
    $new_views = $current_views + 1;
    update_post_meta($chapter_id, '_views_count', $new_views);

    // تسجيل المشاهدة في سجل منفصل للتحليلات المتقدمة
    sekaiplus_log_chapter_view($chapter_id, $user_identifier);

    // تحديث إحصائيات الرواية المرتبطة
    $novel_id = get_post_meta($chapter_id, '_novel_id', true);
    if ($novel_id) {
        sekaiplus_update_novel_view_stats($novel_id);
    }

    return true;
}

/**
 * الحصول على IP الزائر بطريقة آمنة
 */
function sekaiplus_get_visitor_ip() {
    $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');

    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = explode(',', $ip)[0];
            }
            $ip = trim($ip);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }

    return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
}

/**
 * تسجيل مشاهدة الفصل في سجل منفصل للتحليلات
 */
function sekaiplus_log_chapter_view($chapter_id, $user_identifier) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'sekaiplus_chapter_views_log';

    // إنشاء الجدول إذا لم يكن موجوداً
    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        chapter_id bigint(20) NOT NULL,
        user_identifier varchar(255) NOT NULL,
        user_id bigint(20) DEFAULT NULL,
        ip_address varchar(45) NOT NULL,
        user_agent text,
        view_date datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY chapter_id (chapter_id),
        KEY user_identifier (user_identifier),
        KEY view_date (view_date)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // تسجيل المشاهدة
    $wpdb->insert(
        $table_name,
        array(
            'chapter_id' => $chapter_id,
            'user_identifier' => $user_identifier,
            'user_id' => is_user_logged_in() ? get_current_user_id() : null,
            'ip_address' => sekaiplus_get_visitor_ip(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'view_date' => current_time('mysql')
        ),
        array('%d', '%s', '%d', '%s', '%s', '%s')
    );
}

/**
 * تحديث إحصائيات مشاهدات الرواية
 */
function sekaiplus_update_novel_view_stats($novel_id) {
    // الحصول على إجمالي مشاهدات جميع فصول الرواية
    global $wpdb;

    $total_views = $wpdb->get_var($wpdb->prepare("
        SELECT SUM(CAST(meta_value AS UNSIGNED))
        FROM {$wpdb->postmeta} pm
        INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
        WHERE pm.meta_key = '_views_count'
        AND p.post_type = 'chapter'
        AND p.post_status = 'publish'
        AND EXISTS (
            SELECT 1 FROM {$wpdb->postmeta} pm2
            WHERE pm2.post_id = p.ID
            AND pm2.meta_key = '_novel_id'
            AND pm2.meta_value = %d
        )
    ", $novel_id));

    // تحديث إجمالي مشاهدات الرواية
    if ($total_views) {
        update_post_meta($novel_id, '_total_chapter_views', $total_views);

        // تحديث النظام القديم للتوافق
        update_post_meta($novel_id, '_views_count', $total_views);
    }
}

/**
 * الحصول على إحصائيات مشاهدات الفصل المتقدمة
 */
function sekaiplus_get_chapter_view_stats($chapter_id) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'sekaiplus_chapter_views_log';

    // التحقق من وجود الجدول
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        return array(
            'total_views' => (int)get_post_meta($chapter_id, '_views_count', true),
            'unique_views' => 0,
            'today_views' => 0,
            'week_views' => 0,
            'month_views' => 0
        );
    }

    $stats = array();

    // إجمالي المشاهدات
    $stats['total_views'] = (int)get_post_meta($chapter_id, '_views_count', true);

    // المشاهدات الفريدة
    $stats['unique_views'] = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(DISTINCT user_identifier)
        FROM $table_name
        WHERE chapter_id = %d
    ", $chapter_id));

    // مشاهدات اليوم
    $stats['today_views'] = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(*)
        FROM $table_name
        WHERE chapter_id = %d
        AND DATE(view_date) = CURDATE()
    ", $chapter_id));

    // مشاهدات الأسبوع
    $stats['week_views'] = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(*)
        FROM $table_name
        WHERE chapter_id = %d
        AND view_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ", $chapter_id));

    // مشاهدات الشهر
    $stats['month_views'] = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(*)
        FROM $table_name
        WHERE chapter_id = %d
        AND view_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ", $chapter_id));

    return $stats;
}

/**
 * تنظيف سجل المشاهدات القديمة (أكثر من 6 أشهر)
 */
function sekaiplus_cleanup_old_view_logs() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'sekaiplus_chapter_views_log';

    // حذف السجلات الأقدم من 6 أشهر
    $wpdb->query("
        DELETE FROM $table_name
        WHERE view_date < DATE_SUB(NOW(), INTERVAL 6 MONTH)
    ");
}

// جدولة تنظيف السجلات القديمة أسبوعياً
if (!wp_next_scheduled('sekaiplus_cleanup_view_logs')) {
    wp_schedule_event(time(), 'weekly', 'sekaiplus_cleanup_view_logs');
}
add_action('sekaiplus_cleanup_view_logs', 'sekaiplus_cleanup_old_view_logs');

/**
 * عرض إحصائيات المشاهدات للمسؤولين
 */
function sekaiplus_display_admin_view_stats($chapter_id) {
    if (!current_user_can('manage_options')) {
        return '';
    }

    $stats = sekaiplus_get_chapter_view_stats($chapter_id);

    $output = '<div class="admin-view-stats" style="background: #f0f8ff; border: 1px solid #0073aa; border-radius: 4px; padding: 10px; margin: 10px 0; font-size: 12px;">';
    $output .= '<strong>إحصائيات المشاهدات (للمسؤولين فقط):</strong><br>';
    $output .= 'إجمالي المشاهدات: ' . number_format($stats['total_views']) . '<br>';
    $output .= 'المشاهدات الفريدة: ' . number_format($stats['unique_views']) . '<br>';
    $output .= 'مشاهدات اليوم: ' . number_format($stats['today_views']) . '<br>';
    $output .= 'مشاهدات الأسبوع: ' . number_format($stats['week_views']) . '<br>';
    $output .= 'مشاهدات الشهر: ' . number_format($stats['month_views']) . '<br>';
    $output .= '</div>';

    return $output;
}

/**
 * إضافة عمود المشاهدات في قائمة الفصول بلوحة الإدارة
 */
function sekaiplus_add_chapter_views_column($columns) {
    $columns['chapter_views'] = 'المشاهدات';
    return $columns;
}
add_filter('manage_chapter_posts_columns', 'sekaiplus_add_chapter_views_column');

/**
 * عرض بيانات المشاهدات في عمود الإدارة
 */
function sekaiplus_show_chapter_views_column($column, $post_id) {
    if ($column === 'chapter_views') {
        $views = (int)get_post_meta($post_id, '_views_count', true);
        echo '<strong>' . number_format($views) . '</strong>';

        if (function_exists('sekaiplus_get_chapter_view_stats')) {
            $stats = sekaiplus_get_chapter_view_stats($post_id);
            echo '<br><small style="color: #666;">';
            echo 'اليوم: ' . $stats['today_views'] . ' | ';
            echo 'الأسبوع: ' . $stats['week_views'];
            echo '</small>';
        }
    }
}
add_action('manage_chapter_posts_custom_column', 'sekaiplus_show_chapter_views_column', 10, 2);

/**
 * جعل عمود المشاهدات قابل للترتيب
 */
function sekaiplus_make_chapter_views_sortable($columns) {
    $columns['chapter_views'] = '_views_count';
    return $columns;
}
add_filter('manage_edit-chapter_sortable_columns', 'sekaiplus_make_chapter_views_sortable');

/**
 * معالجة ترتيب المشاهدات
 */
function sekaiplus_chapter_views_orderby($query) {
    if (!is_admin()) {
        return;
    }

    $orderby = $query->get('orderby');
    if ('_views_count' === $orderby) {
        $query->set('meta_key', '_views_count');
        $query->set('orderby', 'meta_value_num');
    }
}
add_action('pre_get_posts', 'sekaiplus_chapter_views_orderby');

/**
 * إضافة تقرير المشاهدات في لوحة الإدارة
 */
function sekaiplus_add_views_report_menu() {
    add_submenu_page(
        'edit.php?post_type=chapter',
        'تقرير المشاهدات',
        'تقرير المشاهدات',
        'manage_options',
        'chapter-views-report',
        'sekaiplus_views_report_page'
    );
}
add_action('admin_menu', 'sekaiplus_add_views_report_menu');

/**
 * صفحة تقرير المشاهدات
 */
function sekaiplus_views_report_page() {
    global $wpdb;

    echo '<div class="wrap">';
    echo '<h1>تقرير مشاهدات الفصول</h1>';

    // الفصول الأكثر مشاهدة
    $top_chapters = $wpdb->get_results("
        SELECT p.ID, p.post_title, pm.meta_value as views_count,
               pm2.meta_value as novel_id
        FROM {$wpdb->posts} p
        LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_views_count'
        LEFT JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_novel_id'
        WHERE p.post_type = 'chapter' AND p.post_status = 'publish'
        ORDER BY CAST(pm.meta_value AS UNSIGNED) DESC
        LIMIT 20
    ");

    echo '<div style="display: flex; gap: 20px;">';

    // الجدول الأول: الفصول الأكثر مشاهدة
    echo '<div style="flex: 1;">';
    echo '<h2>الفصول الأكثر مشاهدة</h2>';
    echo '<table class="wp-list-table widefat fixed striped">';
    echo '<thead><tr><th>الفصل</th><th>الرواية</th><th>المشاهدات</th></tr></thead>';
    echo '<tbody>';

    foreach ($top_chapters as $chapter) {
        $novel = get_post($chapter->novel_id);
        $novel_title = $novel ? $novel->post_title : 'غير محدد';

        echo '<tr>';
        echo '<td><a href="' . get_edit_post_link($chapter->ID) . '">' . esc_html($chapter->post_title) . '</a></td>';
        echo '<td>' . esc_html($novel_title) . '</td>';
        echo '<td><strong>' . number_format($chapter->views_count) . '</strong></td>';
        echo '</tr>';
    }

    echo '</tbody></table>';
    echo '</div>';

    // إحصائيات عامة
    echo '<div style="flex: 1;">';
    echo '<h2>إحصائيات عامة</h2>';

    $total_views = $wpdb->get_var("
        SELECT SUM(CAST(meta_value AS UNSIGNED))
        FROM {$wpdb->postmeta} pm
        INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
        WHERE pm.meta_key = '_views_count'
        AND p.post_type = 'chapter'
        AND p.post_status = 'publish'
    ");

    $total_chapters = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->posts}
        WHERE post_type = 'chapter'
        AND post_status = 'publish'
    ");

    $avg_views = $total_chapters > 0 ? round($total_views / $total_chapters, 2) : 0;

    echo '<div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">';
    echo '<p><strong>إجمالي المشاهدات:</strong> ' . number_format($total_views) . '</p>';
    echo '<p><strong>إجمالي الفصول:</strong> ' . number_format($total_chapters) . '</p>';
    echo '<p><strong>متوسط المشاهدات لكل فصل:</strong> ' . number_format($avg_views) . '</p>';
    echo '</div>';

    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * AJAX: تسجيل مشاهدة ذكية للفصل
 */
function sekaiplus_ajax_record_smart_chapter_view() {
    // التحقق من الأمان
    if (!wp_verify_nonce($_POST['nonce'], 'sekaiplus_ajax_nonce')) {
        wp_die('Security check failed');
    }

    $chapter_id = intval($_POST['chapter_id']);
    $reading_time = intval($_POST['reading_time']);
    $scroll_percentage = floatval($_POST['scroll_percentage']);

    // التحقق من صحة البيانات
    if (!$chapter_id || $reading_time < 5000 || $scroll_percentage < 0.1) {
        wp_send_json_error('Invalid view data');
        return;
    }

    // تسجيل المشاهدة
    $result = sekaiplus_smart_increment_chapter_views($chapter_id);

    if ($result) {
        $new_count = (int)get_post_meta($chapter_id, '_views_count', true);
        $stats = sekaiplus_get_chapter_view_stats($chapter_id);

        wp_send_json_success(array(
            'new_count' => $new_count,
            'today_views' => $stats['today_views'],
            'message' => 'View recorded successfully'
        ));
    } else {
        wp_send_json_error('Failed to record view');
    }
}
add_action('wp_ajax_record_smart_chapter_view', 'sekaiplus_ajax_record_smart_chapter_view');
add_action('wp_ajax_nopriv_record_smart_chapter_view', 'sekaiplus_ajax_record_smart_chapter_view');

/**
 * AJAX: الحصول على إحصائيات مشاهدات الفصل
 */
function sekaiplus_ajax_get_chapter_view_stats() {
    // التحقق من الأمان
    if (!wp_verify_nonce($_POST['nonce'], 'sekaiplus_ajax_nonce')) {
        wp_die('Security check failed');
    }

    $chapter_id = intval($_POST['chapter_id']);

    if (!$chapter_id) {
        wp_send_json_error('Invalid chapter ID');
        return;
    }

    $stats = sekaiplus_get_chapter_view_stats($chapter_id);
    wp_send_json_success($stats);
}
add_action('wp_ajax_get_chapter_view_stats', 'sekaiplus_ajax_get_chapter_view_stats');
add_action('wp_ajax_nopriv_get_chapter_view_stats', 'sekaiplus_ajax_get_chapter_view_stats');

/**
 * إضافة إعجاب للفصل
 */
function sekaiplus_toggle_chapter_like() {
    if (!is_user_logged_in()) {
        wp_send_json_error('يجب تسجيل الدخول أولاً');
    }

    $chapter_id = isset($_POST['chapter_id']) ? intval($_POST['chapter_id']) : 0;
    if (!$chapter_id) {
        wp_send_json_error('معرف الفصل غير صالح');
    }

    $user_id = get_current_user_id();
    $likes = get_post_meta($chapter_id, '_likes_users', true);
    if (!is_array($likes)) {
        $likes = array();
    }

    if (in_array($user_id, $likes)) {
        $likes = array_diff($likes, array($user_id));
        $action = 'unliked';
    } else {
        $likes[] = $user_id;
        $action = 'liked';
    }
    
    update_post_meta($chapter_id, '_likes_users', $likes);
    update_post_meta($chapter_id, '_likes_count', count($likes));

    wp_send_json_success(array(
        'action' => $action,
        'likes_count' => count($likes)
    ));
}
add_action('wp_ajax_sekaiplus_toggle_chapter_like', 'sekaiplus_toggle_chapter_like');
add_action('wp_ajax_nopriv_sekaiplus_toggle_chapter_like', 'sekaiplus_toggle_chapter_like');

/**
 * الحصول على جميع ترجمات فصل معين
 */
function sekaiplus_get_chapter_translations($chapter_id) {
    $chapter = get_post($chapter_id);
    if (!$chapter || $chapter->post_type !== 'chapter') {
        return array();
    }

    $unique_id = get_post_meta($chapter_id, '_chapter_unique_id', true);
    
    if (empty($unique_id)) {
        $novel_id = get_post_meta($chapter_id, '_novel_id', true);
        $chapter_number = get_post_meta($chapter_id, '_chapter_number', true);
        $volume_number = get_post_meta($chapter_id, '_volume_number', true);

        $translations = get_posts(array(
            'post_type' => 'chapter',
            'posts_per_page' => -1,
            'post__not_in' => array($chapter_id),
            'meta_query' => array(
                'relation' => 'AND',
                array(
                    'key' => '_novel_id',
                    'value' => $novel_id
                ),
                array(
                    'key' => '_volume_number',
                    'value' => $volume_number
                ),
                array(
                    'key' => '_chapter_number',
                    'value' => $chapter_number
                )
            ),
            'orderby' => 'post_date',
            'order' => 'DESC'
        ));
    } else {
        $translations = get_posts(array(
            'post_type' => 'chapter',
            'posts_per_page' => -1,
            'post__not_in' => array($chapter_id),
            'meta_query' => array(
                array(
                    'key' => '_chapter_unique_id',
                    'value' => $unique_id
                )
            ),
            'orderby' => 'post_date',
            'order' => 'DESC'
        ));
    }

    return array_map(function($translation) {
        $translator_id = $translation->post_author;
        return array(
            'ID' => $translation->ID,
            'chapter_id' => $translation->ID,
            'unique_id' => get_post_meta($translation->ID, '_chapter_unique_id', true),
            'translator_id' => $translator_id,
            'translator_name' => get_the_author_meta('display_name', $translator_id),
            'translator_avatar' => get_avatar_url($translator_id, array('size' => 32)),
            'date' => get_the_date('Y-m-d', $translation->ID),
            'status' => get_post_status($translation->ID),
            'likes_count' => intval(get_post_meta($translation->ID, '_likes_count', true)),
            'views_count' => intval(get_post_meta($translation->ID, '_views_count', true))
        );
    }, $translations);
}

/**
 * تحديث حالة الفصل
 */
function sekaiplus_update_chapter_status($chapter_id, $status, $reason = '') {
    $current_user_id = get_current_user_id();
    
    update_post_meta($chapter_id, '_status', $status);
    
    if ($status === 'rejected' && !empty($reason)) {
        update_post_meta($chapter_id, '_rejection_reason', $reason);
        update_post_meta($chapter_id, '_rejected_by', $current_user_id);
        update_post_meta($chapter_id, '_rejected_at', current_time('mysql'));
    } elseif ($status === 'published') {
        update_post_meta($chapter_id, '_approved_by', $current_user_id);
        update_post_meta($chapter_id, '_approved_at', current_time('mysql'));
    }
    
    return true;
}

/**
 * الحصول على معلومات الفصل
 */
function sekaiplus_get_chapter_info($chapter_id) {
    return array(
        'unique_id' => get_post_meta($chapter_id, '_chapter_unique_id', true),
        'chapter_number' => get_post_meta($chapter_id, '_chapter_number', true),
        'volume_number' => get_post_meta($chapter_id, '_volume_number', true),
        'novel_id' => get_post_meta($chapter_id, '_novel_id', true),
        'status' => get_post_meta($chapter_id, '_status', true),
        'views' => get_post_meta($chapter_id, '_views', true),
        'translator_id' => get_post_field('post_author', $chapter_id),
        'translations' => sekaiplus_get_chapter_translations($chapter_id)
    );
}

/**
 * إنشاء فصل جديد مع دعم الترجمات
 * 
 * @param array $chapter_data بيانات الفصل
 * @return int|WP_Error معرف الفصل الجديد أو كائن خطأ
 */
function sekaiplus_create_chapter($chapter_data) {
    if (empty($chapter_data['title']) || empty($chapter_data['content']) || empty($chapter_data['novel_id']) || empty($chapter_data['translator_id'])) {
        return new WP_Error('missing_data', 'البيانات المطلوبة غير مكتملة');
    }

    $novel = get_post($chapter_data['novel_id']);
    if (!$novel || $novel->post_type !== 'novel') {
        return new WP_Error('invalid_novel', 'الرواية غير موجودة');
    }

    $chapter_unique_id = !empty($chapter_data['chapter_unique_id']) ? 
        $chapter_data['chapter_unique_id'] : 
        sekaiplus_generate_chapter_id();

    $existing_chapter = get_posts(array(
        'post_type' => 'chapter',
        'posts_per_page' => 1,
        'author' => $chapter_data['translator_id'],
        'meta_query' => array(
            'relation' => 'OR',
            array(
                'key' => '_chapter_unique_id',
                'value' => $chapter_unique_id
            )
        )
    ));

    if (!empty($existing_chapter)) {
        return new WP_Error('duplicate_chapter', 'يوجد بالفعل ترجمة لهذا الفصل من نفس المترجم');
    }

    $chapter_post = array(
        'post_title' => wp_strip_all_tags($chapter_data['title']),
        'post_content' => $chapter_data['content'],
        'post_status' => 'publish',
        'post_type' => 'chapter',
        'post_author' => $chapter_data['translator_id'],
    );

    $chapter_id = wp_insert_post($chapter_post);

    if (!is_wp_error($chapter_id)) {
        update_post_meta($chapter_id, '_chapter_unique_id', $chapter_unique_id);
        update_post_meta($chapter_id, '_novel_id', $chapter_data['novel_id']);
        update_post_meta($chapter_id, '_chapter_number', $chapter_data['chapter_number'] ?? '');
        update_post_meta($chapter_id, '_chapter_volume', $chapter_data['volume_id'] ?? '');
        update_post_meta($chapter_id, '_views_count', 0);
        update_post_meta($chapter_id, '_likes_count', 0);
        
        $total_chapters = intval(get_post_meta($chapter_data['novel_id'], '_total_chapters', true));
        update_post_meta($chapter_data['novel_id'], '_total_chapters', $total_chapters + 1);
        
        wp_cache_delete('chapter_' . $chapter_unique_id, 'chapter_translations');
    }

    return $chapter_id;
}

/**
 * إضافة وظائف لدعم فصل الترجمات بشكل نهائي عبر حقل meta جديد
 */
function sekaiplu_set_translation_meta($post_id, $post) {
    // تأكد من أن هذا فصل جديد أو تم تحديثه
    if ($post->post_type !== 'chapter' || (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE)) {
        return;
    }
    
    // إضافة حقل ميتا يجمع بين معرف الفصل الفريد ومعرف المترجم لكل ترجمة
    $chapter_unique_id = get_post_meta($post_id, '_chapter_unique_id', true);
    if (!empty($chapter_unique_id)) {
        $translator_id = $post->post_author;
        // إنشاء معرف فريد للترجمة يجمع بين معرف الفصل الفريد ومعرف المترجم
        $translation_id = $chapter_unique_id . '_' . $translator_id;
        update_post_meta($post_id, '_translation_unique_id', $translation_id);
    }
}
add_action('save_post', 'sekaiplu_set_translation_meta', 10, 2);

/**
 * تعبئة حقل الترجمة الفريد للفصول القديمة
 */
function sekaiplu_fill_translation_unique_id() {
    if (!isset($_GET['fill_translation_ids']) || !current_user_can('manage_options')) {
        return;
    }
    
    $chapters = get_posts(array(
        'post_type' => 'chapter',
        'posts_per_page' => -1,
        'meta_query' => array(
            'relation' => 'OR',
            array(
                'key' => '_translation_unique_id',
                'compare' => 'NOT EXISTS'
            ),
            array(
                'key' => '_translation_unique_id',
                'value' => '',
                'compare' => '='
            )
        )
    ));
    
    $count = 0;
    foreach ($chapters as $chapter) {
        $chapter_unique_id = get_post_meta($chapter->ID, '_chapter_unique_id', true);
        if (!empty($chapter_unique_id)) {
            $translation_id = $chapter_unique_id . '_' . $chapter->post_author;
            update_post_meta($chapter->ID, '_translation_unique_id', $translation_id);
            $count++;
        }
    }
    
    add_action('admin_notices', function() use ($count) {
        echo '<div class="notice notice-success"><p>' . sprintf('تم تحديث %d فصل بمعرفات ترجمة فريدة.', $count) . '</p></div>';
    });
}
add_action('admin_init', 'sekaiplu_fill_translation_unique_id');

/**
 * Modify chapter permalink to include translator name
 */
function sekaiplus_modify_chapter_permalink($url, $post) {
    if ($post->post_type === 'chapter') {
        $chapter_id = get_post_meta($post->ID, '_chapter_unique_id', true);
        
        if (!empty($chapter_id)) {
            return home_url("chapter/?r={$chapter_id}");
        }
    }
    return $url;
}
add_filter('post_type_link', 'sekaiplus_modify_chapter_permalink', 10, 2);

/**
 * Comment callback function
 */
function sekaiplus_comment_callback($comment, $args, $depth) {
    $GLOBALS['comment'] = $comment;
    $comment_author_url = 'u/' . $comment->comment_author;
    $likes_count = sekaiplus_get_comment_likes_count(get_comment_ID());
    $has_liked = sekaiplus_user_has_liked_comment(get_comment_ID());
    ?>
    <div <?php comment_class('comment-item'); ?> id="comment-<?php comment_ID(); ?>" data-comment-id="<?php comment_ID(); ?>">
        <div class="comment-body">
            <div class="comment-header d-flex">
                <div class="comment-author-avatar">
                    <a href="<?php echo esc_url(home_url($comment_author_url)); ?>">
                        <?php echo get_avatar($comment, $args['avatar_size']); ?>
                    </a>
                </div>
                <div class="comment-meta flex-grow-1">
                    <div class="comment-author-info">
                        <a href="<?php echo esc_url(home_url($comment_author_url)); ?>" class="comment-author-name">
                            u/<?php echo get_comment_author(); ?>
                        </a>
                        <span class="comment-time" title="<?php echo get_comment_date('Y-m-d H:i:s'); ?>">
                            <?php 
                            $time_diff = human_time_diff(get_comment_time('U'), current_time('timestamp'));
                            printf(__('منذ %s', 'sekaiplus'), $time_diff);
                            ?>
                        </span>
                        <?php if ($comment->comment_approved == '0') : ?>
                            <span class="comment-pending">
                                <i class="fas fa-clock me-1"></i>
                                <?php _e('في انتظار المراجعة', 'sekaiplus'); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="comment-content">
                <?php comment_text(); ?>
            </div>

            <div class="comment-actions">
                <button class="comment-like-btn <?php echo $has_liked ? 'active' : ''; ?>" data-comment-id="<?php comment_ID(); ?>">
                    <i class="<?php echo $has_liked ? 'fas' : 'far'; ?> fa-thumbs-up"></i>
                    <span class="like-count"><?php echo $likes_count; ?></span>
                </button>

                <?php if (comments_open()) : ?>
                    <button class="comment-reply-btn" data-comment-id="<?php comment_ID(); ?>">
                        <i class="fas fa-reply"></i>
                        <?php _e('رد', 'sekaiplus'); ?>
                    </button>
                <?php endif; ?>

                <?php if ($depth === 1 && get_comments(array('parent' => get_comment_ID(), 'count' => true)) > 0) : ?>
                    <button class="show-replies-btn">
                        <i class="fas fa-chevron-down"></i>
                        <span class="replies-count">
                            <?php printf(_n('%s رد', '%s ردود', get_comments(array('parent' => get_comment_ID(), 'count' => true)), 'sekaiplus'), number_format_i18n(get_comments(array('parent' => get_comment_ID(), 'count' => true)))); ?>
                        </span>
                    </button>
                <?php endif; ?>
            </div>

            <?php if ($depth === 1) : ?>
                <div class="comment-replies" style="display: none;">
                    <?php
                    $replies = get_comments(array(
                        'parent' => get_comment_ID(),
                        'status' => 'approve',
                        'order' => 'ASC'
                    ));
                    
                    if ($replies) {
                        foreach ($replies as $reply) {
                            sekaiplus_comment_callback($reply, $args, $depth + 1);
                        }
                    }
                    ?>
                </div>
            <?php endif; ?>
        </div>
    <?php
}

/**
 * إضافة دعم AJAX للتعليقات
 */
function sekaiplus_enqueue_comment_scripts() {
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'sekaiplus_enqueue_comment_scripts');

// تضمين ملف وظائف AJAX للروايات
require get_template_directory() . '/inc/novels-ajax.php';

// إضافة متغيرات AJAX للجافاسكريبت
function sekaiplus_enqueue_novels_ajax_scripts() {
    wp_enqueue_script('sekaiplus-novels-ajax', get_template_directory_uri() . '/assets/js/novels-ajax.js', array('jquery'), SEKAIPLUS_VERSION, true);
    
    wp_localize_script('sekaiplus-novels-ajax', 'sekaiplus_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('sekaiplus_ajax_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'sekaiplus_enqueue_novels_ajax_scripts');

/**
 * إضافة نقطة نهاية لحفظ الإعجابات
 */
function sekaiplus_handle_comment_like() {
    if (!is_user_logged_in()) {
        wp_send_json_error('يجب تسجيل الدخول أولاً');
    }

    $comment_id = intval($_POST['comment_id']);
    $user_id = get_current_user_id();
    
    if (!$comment_id) {
        wp_send_json_error('معرف التعليق غير صالح');
    }

    $likes = get_comment_meta($comment_id, 'comment_likes', true);
    $likes = $likes ? json_decode($likes, true) : array();
    
    if (in_array($user_id, $likes)) {
        $likes = array_diff($likes, array($user_id));
        $action = 'unliked';
    } else {
        $likes[] = $user_id;
        $action = 'liked';
    }
    
    update_comment_meta($comment_id, 'comment_likes', json_encode(array_values($likes)));
    
    wp_send_json_success(array(
        'action' => $action,
        'count' => count($likes)
    ));
}
add_action('wp_ajax_sekaiplus_comment_like', 'sekaiplus_handle_comment_like');
add_action('wp_ajax_nopriv_sekaiplus_comment_like', function() {
    wp_send_json_error('يجب تسجيل الدخول أولاً');
});

/**
 * دالة لعرض عدد الإعجابات الحالي
 */
function sekaiplus_get_comment_likes_count($comment_id) {
    $likes = get_comment_meta($comment_id, 'comment_likes', true);
    return $likes ? count(json_decode($likes, true)) : 0;
}

/**
 * دالة للتحقق مما إذا كان المستخدم قد سجل إعجابه
 */
function sekaiplus_user_has_liked_comment($comment_id) {
    if (!is_user_logged_in()) return false;
    
    $likes = get_comment_meta($comment_id, 'comment_likes', true);
    if (!$likes) return false;
    
    $likes = json_decode($likes, true);
    return in_array(get_current_user_id(), $likes);
}

/**
 * u0625u0639u0627u062fu0629 u062au0648u062cu064au0647 u0635u0641u062du0627u062a u062au0633u062cu064au0644 u0627u0644u062fu062eu0648u0644 u0648u0627u0644u062au0633u062cu064au0644 u0627u0644u0627u0641u062au0631u0627u0636u064au0629 u0644u0648u0648u0631u062bu0631u0628u0631u064au0633 u0625u0644u0649 u0635u0641u062du0627u062a u0645u062eu0635u0635u0629
 */
function sekai_custom_login_register_pages() {
    // u0627u0644u062au062du0642u0642 u0645u0646 u0648u062cu0648u062f u0635u0641u062du0629 u062au0633u062cu064au0644 u0627u0644u062fu062eu0648u0644 u0627u0644u0645u062eu0635u0635u0629
    $login_page = get_page_by_path('login');
    $login_page_id = $login_page ? $login_page->ID : 0;
    
    // u0627u0644u062au062du0642u0642 u0645u0646 u0648u062cu0648u062f u0635u0641u062du0629 u0627u0644u062au0633u062cu064au0644 u0627u0644u0645u062eu0635u0635u0629
    $register_page = get_page_by_path('register');
    $register_page_id = $register_page ? $register_page->ID : 0;
    
    // u0625u0630u0627 u0643u0627u0646u062a u0635u0641u062du0629 u062au0633u062cu064au0644 u0627u0644u062fu062eu0648u0644 u0645u0648u062cu0648u062fu0629
    if ($login_page_id) {
        // u0625u0639u0627u062fu0629 u062au0648u062cu064au0647 u0635u0641u062du0629 u062au0633u062cu064au0644 u0627u0644u062fu062eu0648u0644 u0627u0644u0627u0641u062au0631u0627u0636u064au0629
        if (!is_user_logged_in() && $GLOBALS['pagenow'] === 'wp-login.php' && !isset($_GET['action'])) {
            wp_redirect(get_permalink($login_page_id));
            exit();
        }
    }
    
    // u0625u0630u0627 u0643u0627u0646u062a u0635u0641u062du0629 u0627u0644u062au0633u062cu064au0644 u0645u0648u062cu0648u062fu0629
    if ($register_page_id) {
        // u0625u0639u0627u062fu0629 u062au0648u062cu064au0647 u0635u0641u062du0629 u0627u0644u062au0633u062cu064au0644 u0627u0644u0627u0641u062au0631u0627u0636u064au0629
        if (!is_user_logged_in() && $GLOBALS['pagenow'] === 'wp-login.php' && isset($_GET['action']) && $_GET['action'] === 'register') {
            wp_redirect(get_permalink($register_page_id));
            exit();
        }
    }
}
add_action('init', 'sekai_custom_login_register_pages');

/**
 * u062au0639u062fu064au0644 u0631u0648u0627u0628u0637 u062au0633u062cu064au0644 u0627u0644u062fu062eu0648u0644 u0648u0627u0644u062au0633u062cu064au0644 u0641u064a u0627u0644u0642u0627u0644u0628
 */
function sekai_custom_login_url($login_url, $redirect = '', $force_reauth = false) {
    $login_page = get_page_by_path('login');
    if ($login_page) {
        $login_url = get_permalink($login_page->ID);
        if (!empty($redirect)) {
            $login_url = add_query_arg('redirect_to', urlencode($redirect), $login_url);
        }
        if ($force_reauth) {
            $login_url = add_query_arg('reauth', '1', $login_url);
        }
    }
    return $login_url;
}
add_filter('login_url', 'sekai_custom_login_url', 10, 3);

/**
 * u062au0639u062fu064au0644 u0631u0627u0628u0637 u0627u0644u062au0633u062cu064au0644
 */
function sekai_custom_register_url($register_url) {
    $register_page = get_page_by_path('register');
    if ($register_page) {
        $register_url = get_permalink($register_page->ID);
    }
    return $register_url;
}
add_filter('register_url', 'sekai_custom_register_url');

/**
 * u062au0639u062fu064au0644 u0631u0627u0628u0637 u0627u0633u062au062au062fu0629 u0643u0644u0645u0629 u0627u0644u0645u0631u0648u0631
 */
function sekai_custom_lostpassword_url($lostpassword_url, $redirect = '') {
    $login_page = get_page_by_path('login');
    if ($login_page) {
        $lostpassword_url = add_query_arg('action', 'lostpassword', get_permalink($login_page->ID));
        if (!empty($redirect)) {
            $lostpassword_url = add_query_arg('redirect_to', urlencode($redirect), $lostpassword_url);
        }
    }
    return $lostpassword_url;
}
add_filter('lostpassword_url', 'sekai_custom_lostpassword_url', 10, 2);

/**
 * إعادة توجيه صفحة استعادة كلمة المرور الافتراضية
 */
function sekai_redirect_lostpassword_page() {
    $lostpassword_page = get_page_by_path('lostpassword');
    if ($lostpassword_page && !is_user_logged_in() && $GLOBALS['pagenow'] === 'wp-login.php' && isset($_GET['action']) && $_GET['action'] === 'lostpassword') {
        wp_redirect(get_permalink($lostpassword_page->ID));
        exit();
    }
}
add_action('init', 'sekai_redirect_lostpassword_page');

/**
 * Configure SMTP for WordPress email
 */
function sekaiplus_configure_smtp($phpmailer) {
    // SMTP configuration
    $phpmailer->isSMTP();
    $phpmailer->Host = 'smtp.gmail.com'; // Replace with your SMTP host
    $phpmailer->SMTPAuth = true;
    $phpmailer->Port = 587;
    $phpmailer->Username = '<EMAIL>'; // Replace with your email
    $phpmailer->Password = 'vxku gtfy wxhc mxnv'; // Replace with your password or app password
    $phpmailer->SMTPSecure = 'tls';
    $phpmailer->From = '<EMAIL>'; // Replace with your email
    $phpmailer->FromName = get_bloginfo('name');
}
add_action('phpmailer_init', 'sekaiplus_configure_smtp');

// Enable debugging for email if needed
// add_filter('wp_mail_failed', function($error) {
//     error_log('WordPress mail error: ' . print_r($error, true));
//     return $error;
// });

/**
 * Cloudflare Turnstile Integration
 */
function sekaiplus_turnstile_site_key() {
    return '0x4AAAAAAA7S2nGWaiIYImk7';
}

function sekaiplus_turnstile_secret_key() {
    return '0x4AAAAAAA7S2p9gt-762lF_8TevrTLkxs4';
}

function sekaiplus_verify_turnstile($token) {
    $secret_key = sekaiplus_turnstile_secret_key();
    $response = wp_remote_post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
        'body' => [
            'secret' => $secret_key,
            'response' => $token,
            'remoteip' => $_SERVER['REMOTE_ADDR']
            ]
    ]);
    
    if (is_wp_error($response)) {
        return false;
    }
    
    $body = wp_remote_retrieve_body($response);
    $result = json_decode($body, true);
    
    return isset($result['success']) && $result['success'] === true;
}

function sekaiplus_turnstile_script() {
    wp_enqueue_script('cloudflare-turnstile', 'https://challenges.cloudflare.com/turnstile/v0/api.js', [], null, true);
}
add_action('wp_enqueue_scripts', 'sekaiplus_turnstile_script');

/**
 * تفعيل التعليقات لأنواع المنشورات المخصصة
 */
function sekaiplus_enable_comments_for_custom_post_types() {
    add_post_type_support('novel', 'comments');
    add_post_type_support('chapter', 'comments');
}
add_action('init', 'sekaiplus_enable_comments_for_custom_post_types', 11);

/**
 * إصلاح مشكلة JavaScript في نظام التعليقات
 */
function sekaiplus_fix_comments_js() {
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التأكد من تحميل نموذج التعليقات
            console.log('تحميل نظام التعليقات');
            if (document.getElementById('comments') && !document.getElementById('respond')) {
                console.log('نموذج التعليقات غير موجود، جاري إعادة تحميله');
                // يمكن إضافة كود هنا لإعادة تحميل نموذج التعليقات إذا لزم الأمر
            }
        });
        </script>
        <?php
    }
}
add_action('wp_footer', 'sekaiplus_fix_comments_js');

/**
 * إضافة علامات Open Graph للفصول لتحسين المشاركة على وسائل التواصل الاجتماعي
 */
function sekaiplu_add_opengraph_tags() {
    global $post;

    // تطبيق فقط على صفحات الفصول الفردية
    if (!is_singular('chapter')) {
        return;
    }

    // الحصول على بيانات الفصل
    $chapter_number = get_post_meta($post->ID, 'chapter_number', true);
    $chapter_title = get_the_title($post->ID);
    
    // الحصول على الرواية المرتبطة
    $novel_id = get_post_meta($post->ID, 'chapter_parent_novel', true);
    $novel_title = get_the_title($novel_id);
    
    // الحصول على المترجم
    $translator_id = get_post_meta($post->ID, 'chapter_translator', true);
    $translator = get_userdata($translator_id);
    $translator_name = $translator ? $translator->display_name : 'غير معروف';
    
    // استرجاع صورة الرواية
    $novel_image = get_the_post_thumbnail_url($novel_id, 'large');
    if (!$novel_image) {
        $novel_image = get_template_directory_uri() . '/images/default-novel.jpg';
    }
    $novel_image = esc_url($novel_image);

    // الحصول على أبعاد الصورة
    list($width, $height) = @getimagesize($novel_image);
    $width = $width ?: 1200;
    $height = $height ?: 630;

    // إنشاء وصف مخصص
    $description = sprintf(
        'الفصل %s: %s | الرواية: %s | المترجم: %s', 
        $chapter_number, 
        $chapter_title, 
        $novel_title, 
        $translator_name
    );

    // إضافة وسوم Open Graph
    echo "
        <!-- Open Graph Tags -->
        <meta property='og:locale' content='ar_AR' />
        <meta property='og:type' content='article' />
        <meta property='og:title' content='" . esc_attr($chapter_title) . " - " . esc_attr($novel_title) . "' />
        <meta property='og:description' content='" . esc_attr($description) . "' />
        <meta property='og:url' content='" . esc_url(get_permalink()) . "' />
        <meta property='og:site_name' content='" . esc_attr(get_bloginfo('name')) . "' />
        <meta property='og:image' content='" . esc_url($novel_image) . "' />
        <meta property='og:image:secure_url' content='" . esc_url($novel_image) . "' />
        <meta property='og:image:width' content='" . esc_attr($width) . "' />
        <meta property='og:image:height' content='" . esc_attr($height) . "' />

        <!-- Twitter Card Tags -->
        <meta name='twitter:card' content='summary_large_image' />
        <meta name='twitter:title' content='" . esc_attr($chapter_title) . " - " . esc_attr($novel_title) . "' />
        <meta name='twitter:description' content='" . esc_attr($description) . "' />
        <meta name='twitter:image' content='" . esc_url($novel_image) . "' />
    ";

    // تحسين الشكل الجمالي للنصوص
    echo "
        <!-- تحسين جمال النصوص -->
        <style>
            meta[property^='og:'] {
                display: block;
                margin: 2px 0;
            }
        </style>
    ";
}

// تسجيل الدالة مع Hook wp_head
add_action('wp_head', 'sekaiplu_add_opengraph_tags');

/**
 * منع الدوران المستمر عند تحديث فصول الرسوم التوضيحية
 */
function sekaiplus_stop_spinning_after_save() {
    global $post_type;
    if ($post_type === 'chapter') {
        ?>
<script type="text/javascript">
jQuery(document).ready(function($) {
    // إيقاف hearbeat API مؤقتًا عند تحديد خانة الاختيار
    $('#is_illustration').on('change', function() {
        if ($(this).is(':checked')) {
            // إيقاف heartbeat API مؤقتًا
            wp.heartbeat.interval(0);
            
            // إعادة تفعيله بعد الانتهاء من الحفظ
            $('#publish').on('click', function() {
                setTimeout(function() {
                    wp.heartbeat.interval('standard');
                }, 5000);
            });
        } else {
            // إعادة تفعيل heartbeat API
            wp.heartbeat.interval('standard');
        }
    });
});
</script>
        <?php
    }
}
add_action('admin_footer', 'sekaiplus_stop_spinning_after_save');

//حاسبة عدد الفصول
function sekaiplus_get_unique_chapter_count($novel_id) {
    global $wpdb;
    
    // الاستعلام المباشر باستخدام _novel_id (كما في novel-chapters.php)
    $chapters = $wpdb->get_results($wpdb->prepare(
        "SELECT p.ID, pm.meta_value AS chapter_number 
        FROM {$wpdb->posts} p 
        JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
        WHERE p.post_type = 'chapter' 
        AND p.post_status = 'publish' 
        AND pm.meta_key = '_chapter_number' 
        AND EXISTS (
            SELECT 1 FROM {$wpdb->postmeta} 
            WHERE post_id = p.ID 
            AND meta_key = '_novel_id' 
            AND meta_value = %d
        )",
        $novel_id
    ));
    
    // تحضير مصفوفة لأرقام الفصول الفريدة
    $unique_chapter_numbers = array();
    
    foreach ($chapters as $chapter) {
        if (!empty($chapter->chapter_number)) {
            $unique_chapter_numbers[$chapter->chapter_number] = true;
        }
    }
    
    return count($unique_chapter_numbers);
}

add_action('wp_ajax_sekaiplus_quick_search', 'sekaiplus_quick_search');
add_action('wp_ajax_nopriv_sekaiplus_quick_search', 'sekaiplus_quick_search');
function sekaiplus_quick_search() {
    $query = sanitize_text_field($_POST['query']);
    $args = array(
        'post_type' => array('novel', 'translator'),
        's' => $query,
        'posts_per_page' => 5,
    );
    $search = new WP_Query($args);
    if ($search->have_posts()) {
        echo '<ul class="list-unstyled mb-0">';
        while ($search->have_posts()) {
            $search->the_post();
            echo '<li><a href="' . get_permalink() . '">' . get_the_title() . '</a></li>';
        }
        echo '</ul>';
    } else {
        echo '<div class="text-muted p-2">لا توجد نتائج</div>';
    }
    wp_die();
}

// تضمين ملف نظام الإشعارات أولاً
require_once get_template_directory() . '/inc/notifications-system.php';

// التأكد من إنشاء جدول الإشعارات
add_action('init', function() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_user_notifications';

    // التحقق من وجود الجدول
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        sekai_create_notifications_table();
    }
});

// كود الإشعار التجريبي
add_action('init', function() {
    if (is_user_logged_in() && isset($_GET['test_notify'])) {
        sekai_add_notification(
            get_current_user_id(),
            'system',
            'هذا إشعار تجريبي من الرابط المباشر',
            'إشعار تجريبي'
        );
        exit('تمت إضافة إشعار تجريبي. تحقق من الإشعارات.');
    }
});

add_action('admin_notices', function() {
    global $pagenow;
    // تحقق أننا في صفحة إضافة رواية جديدة
    if ($pagenow === 'post-new.php' && isset($_GET['post_type']) && $_GET['post_type'] === 'novel') {
        // احصل على رابط صفحة الدليل (يفضل استخدام get_permalink إذا كانت صفحة WordPress)
        $guide_url = home_url('/guidenovel/');
        ?>
        <style>
        .novel-guide-notice {
            background: linear-gradient(90deg, #4361ee 60%, #ff6b6b 100%);
            color: #fff;
            border-radius: 14px;
            padding: 22px 28px;
            margin: 25px 0 30px 0;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            box-shadow: 0 6px 32px rgba(44,62,80,0.09);
            display: flex;
            align-items: center;
            justify-content: space-between;
            direction: rtl;
        }
        .novel-guide-notice .notice-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-left: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .novel-guide-notice .guide-btn {
            background: #fff;
            color: #4361ee;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            padding: 10px 28px;
            font-size: 1.1rem;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(67,97,238,0.10);
            transition: background .2s, color .2s;
            text-decoration: none;
        }
        .novel-guide-notice .guide-btn:hover {
            background: #4361ee;
            color: #fff;
        }
        </style>
        <div class="novel-guide-notice">
            <span class="notice-title">
                <span style="font-size:1.6em;vertical-align:middle;">📖</span>
                تفقّد <b>دليل إضافة رواية جديدة</b> قبل تعبئة الحقول!
            </span>
            <a href="<?php echo esc_url($guide_url); ?>" class="guide-btn" target="_blank" rel="noopener">عرض الدليل</a>
        </div>
        <?php
    }
});

add_action('wp_ajax_update_user_bio', function() {
    // تحقق من nonce
    if (!isset($_POST['bio_nonce']) || !wp_verify_nonce($_POST['bio_nonce'], 'update_user_bio')) {
        wp_send_json_error('رمز الأمان غير صحيح.');
    }
    // تحقق من تسجيل الدخول
    if (!is_user_logged_in()) {
        wp_send_json_error('يجب تسجيل الدخول.');
    }
    $user_id = get_current_user_id();
    $bio = sanitize_textarea_field($_POST['description'] ?? '');
    wp_update_user([
        'ID' => $user_id,
        'description' => $bio,
    ]);
    wp_send_json_success('تم تحديث النبذة بنجاح.');
});

add_action('wp_ajax_update_social_media', function() {
    if (!isset($_POST['social_nonce']) || !wp_verify_nonce($_POST['social_nonce'], 'update_social_media')) {
        wp_send_json_error('رمز الأمان غير صحيح.');
    }
    if (!is_user_logged_in()) {
        wp_send_json_error('يجب تسجيل الدخول.');
    }
    $user_id = get_current_user_id();

    update_user_meta($user_id, 'user_url', esc_url_raw($_POST['website'] ?? ''));
    update_user_meta($user_id, 'twitter', sanitize_text_field($_POST['twitter'] ?? ''));
    update_user_meta($user_id, 'instagram', sanitize_text_field($_POST['instagram'] ?? ''));
    update_user_meta($user_id, 'discord', sanitize_text_field($_POST['discord'] ?? ''));
    update_user_meta($user_id, 'telegram', sanitize_text_field($_POST['telegram'] ?? ''));

    wp_send_json_success('تم تحديث وسائل التواصل بنجاح.');
});

add_action('wp_ajax_update_display_name', function() {
    if (!isset($_POST['name_nonce']) || !wp_verify_nonce($_POST['name_nonce'], 'update_display_name')) {
        wp_send_json_error('رمز الأمان غير صحيح.');
    }
    if (!is_user_logged_in()) {
        wp_send_json_error('يجب تسجيل الدخول.');
    }
    $user_id = get_current_user_id();
    $display_name = sanitize_text_field($_POST['display_name'] ?? '');
    wp_update_user([
        'ID' => $user_id,
        'display_name' => $display_name,
    ]);
    wp_send_json_success('تم تحديث اسم العرض بنجاح.');
});